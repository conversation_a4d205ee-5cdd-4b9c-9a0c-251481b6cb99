defmodule HadesWeb.ChatLive.CompanyResearcher do
  @moduledoc """
  Company Researcher page - specialized UI for company research (not a chat experience)
  """
  use HadesWeb, :live_view

  alias HadesWeb.Agents.CompanyResearchAgent

  on_mount(HadesWeb.AdminUserLiveAuth)

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:menu, :chat)
     |> assign(:page_title, "Daily Competitor Intelligence")
     |> assign(:config_confirmed, false)
     |> assign(:ticker, "")
     |> assign(:my_company_ticker, "")
     |> assign(:is_gathering, false)
     |> assign(:is_generating, false)
     |> assign(:gathered_info, nil)
     |> assign(:system_prompt, nil)
     |> assign(:system_prompt_edited, false)
     |> assign(:research_result, nil)
     |> assign(:eval_result, nil)
     |> assign(:is_evaluating, false)
     |> assign(:market_data, nil)
     |> assign(:company_info, nil)
     |> assign(:company_name, nil)
     |> assign(:my_company_info, nil)
     |> assign(:announcement_smart_data, nil)
     |> assign(:announcement_raw_data, nil)
     |> assign(:linkedin_data, nil)
     |> assign(:data_sources_status, %{
       my_company_info: :idle,
       basic_company_info: :idle,
       market_data: :idle,
       announcement_smart: :idle,
       announcement_raw: :idle,
       linkedin: :idle,
       hotcopper: :disabled,
       previous_answer_reg: :disabled
     })
     |> assign(:data_sources_enabled, %{
       market_data: true,
       announcement_smart: true,
       announcement_raw: false,
       linkedin: true,
       hotcopper: false,
       previous_answer_reg: false
     })
     |> assign(:agent_options, %{
       bullet_point_description: false,
       report_what_matters: false,
       auto_competitor: true,
       chain_of_thought: false
     })
     |> assign(:competitors, [])
     |> assign(:competitors_enabled, %{})
     |> assign(:debug_mode, false)
     |> assign(:gathering_start_time, nil)
     |> assign(:email_address, "")
     |> assign(:is_sending_email, false)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
      <!-- Header -->
      <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <.link
                navigate={~p"/chat"}
                class="mr-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <PC.icon name="hero-arrow-left" class="w-5 h-5" />
              </.link>
              <div class="flex items-center">
                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                  <PC.icon name="hero-magnifying-glass" class="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Daily Competitor Intelligence
                  </h1>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    Get daily updates on competitor activities and market movements
                  </p>
                </div>
              </div>
            </div>
            <!-- Reset Button -->
            <div class="flex items-center">
              <button
                phx-click="reset_page"
                class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-all duration-200 text-sm flex items-center gap-2"
                title="Reset page"
              >
                <PC.icon name="hero-arrow-path" class="w-4 h-4" /> Reset
              </button>
            </div>
          </div>
        </div>
      </div>
      
    <!-- Main Content -->
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Step 1: Configure Your Agent -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                1
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                Configure Your Agent
              </h2>
            </div>
          </div>
          <p class="text-gray-600 dark:text-gray-400 mb-6">
            Choose your agent type and select the data sources you want to include in your research.
          </p>
          
    <!-- Agent Options -->
          <div class="mb-8">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Agent Type
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <!-- Bullet Point Description -->
              <div
                class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer"),
                  if(@agent_options.bullet_point_description,
                    do:
                      "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                    else:
                      "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                  ),
                  if(@config_confirmed, do: "opacity-50", else: "")
                ]}
                phx-click={unless @config_confirmed, do: "toggle_agent_option"}
                phx-value-option="bullet_point_description"
              >
                <div class="flex items-center">
                  <PC.icon
                    name="hero-list-bullet"
                    class={[
                      "w-5 h-5 mr-3",
                      if(@agent_options.bullet_point_description,
                        do: "text-green-600 dark:text-green-400",
                        else: "text-gray-400 dark:text-gray-500"
                      )
                    ]}
                  />
                  <div>
                    <div class={[
                      "font-medium",
                      if(@agent_options.bullet_point_description,
                        do: "text-gray-800 dark:text-gray-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      Bullet Point Description
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      Structured daily updates with key points
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class={[
                    "relative inline-flex items-center",
                    if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer")
                  ]}>
                    <input
                      type="checkbox"
                      checked={@agent_options.bullet_point_description}
                      disabled={@config_confirmed}
                      class="sr-only peer"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                    </div>
                  </label>
                </div>
              </div>
              
    <!-- Report What Matters -->
              <div
                class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer"),
                  if(@agent_options.report_what_matters,
                    do:
                      "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                    else:
                      "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                  ),
                  if(@config_confirmed, do: "opacity-50", else: "")
                ]}
                phx-click={unless @config_confirmed, do: "toggle_agent_option"}
                phx-value-option="report_what_matters"
              >
                <div class="flex items-center">
                  <PC.icon
                    name="hero-magnifying-glass"
                    class={[
                      "w-5 h-5 mr-3",
                      if(@agent_options.report_what_matters,
                        do: "text-green-600 dark:text-green-400",
                        else: "text-gray-400 dark:text-gray-500"
                      )
                    ]}
                  />
                  <div>
                    <div class={[
                      "font-medium",
                      if(@agent_options.report_what_matters,
                        do: "text-gray-800 dark:text-gray-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      Report What Matters
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      AI-filtered intelligence focusing on relevance
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class={[
                    "relative inline-flex items-center",
                    if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer")
                  ]}>
                    <input
                      type="checkbox"
                      checked={@agent_options.report_what_matters}
                      disabled={@config_confirmed}
                      class="sr-only peer"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                    </div>
                  </label>
                </div>
              </div>
              
    <!-- Auto Competitor -->
              <div
                class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer"),
                  if(@agent_options.auto_competitor,
                    do:
                      "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                    else:
                      "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                  ),
                  if(@config_confirmed, do: "opacity-50", else: "")
                ]}
                phx-click={unless @config_confirmed, do: "toggle_agent_option"}
                phx-value-option="auto_competitor"
              >
                <div class="flex items-center">
                  <PC.icon
                    name="hero-users"
                    class={[
                      "w-5 h-5 mr-3",
                      if(@agent_options.auto_competitor,
                        do: "text-green-600 dark:text-green-400",
                        else: "text-gray-400 dark:text-gray-500"
                      )
                    ]}
                  />
                  <div>
                    <div class={[
                      "font-medium",
                      if(@agent_options.auto_competitor,
                        do: "text-gray-800 dark:text-gray-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      Auto Find Your Competitors
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      Automatically discover and analyze up to 5 competitors
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class={[
                    "relative inline-flex items-center",
                    if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer")
                  ]}>
                    <input
                      type="checkbox"
                      checked={@agent_options.auto_competitor}
                      disabled={@config_confirmed}
                      class="sr-only peer"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                    </div>
                  </label>
                </div>
              </div>
              
    <!-- Chain of Thought -->
              <div class={[
                "flex items-center justify-between p-3 rounded-lg transition-all duration-300 cursor-not-allowed opacity-50",
                "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
              ]}>
                <div class="flex items-center">
                  <PC.icon name="hero-cpu-chip" class="w-5 h-5 mr-3 text-gray-400 dark:text-gray-500" />
                  <div>
                    <div class="font-medium text-gray-500 dark:text-gray-400">
                      Chain of Thought
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      Coming soon - Advanced reasoning process
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class="relative inline-flex items-center cursor-not-allowed">
                    <input type="checkbox" checked={false} disabled={true} class="sr-only peer" />
                    <div class="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600">
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
    <!-- Data Sources -->
          <div class="mb-8">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Data Sources
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <!-- Market Data -->
              <div
                class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer"),
                  if(@data_sources_enabled.market_data,
                    do:
                      "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                    else:
                      "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                  ),
                  if(@config_confirmed, do: "opacity-50", else: "")
                ]}
                phx-click={unless @config_confirmed, do: "toggle_data_source"}
                phx-value-source="market_data"
              >
                <div class="flex items-center">
                  <PC.icon
                    name="hero-chart-bar"
                    class={[
                      "w-5 h-5 mr-3",
                      if(@data_sources_enabled.market_data,
                        do: "text-green-600 dark:text-green-400",
                        else: "text-gray-400 dark:text-gray-500"
                      )
                    ]}
                  />
                  <div>
                    <div class={[
                      "font-medium",
                      if(@data_sources_enabled.market_data,
                        do: "text-gray-800 dark:text-gray-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      Market Data
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      Stock prices and trading data
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class={[
                    "relative inline-flex items-center",
                    if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer")
                  ]}>
                    <input
                      type="checkbox"
                      checked={@data_sources_enabled.market_data}
                      disabled={@config_confirmed}
                      class="sr-only peer"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                    </div>
                  </label>
                </div>
              </div>
              
    <!-- Smart Announcements -->
              <div
                class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer"),
                  if(@data_sources_enabled.announcement_smart,
                    do:
                      "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                    else:
                      "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                  ),
                  if(@config_confirmed, do: "opacity-50", else: "")
                ]}
                phx-click={unless @config_confirmed, do: "toggle_data_source"}
                phx-value-source="announcement_smart"
              >
                <div class="flex items-center">
                  <PC.icon
                    name="hero-megaphone"
                    class={[
                      "w-5 h-5 mr-3",
                      if(@data_sources_enabled.announcement_smart,
                        do: "text-green-600 dark:text-green-400",
                        else: "text-gray-400 dark:text-gray-500"
                      )
                    ]}
                  />
                  <div>
                    <div class={[
                      "font-medium",
                      if(@data_sources_enabled.announcement_smart,
                        do: "text-gray-800 dark:text-gray-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      Smart Announcements
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      AI-processed company announcements
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class={[
                    "relative inline-flex items-center",
                    if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer")
                  ]}>
                    <input
                      type="checkbox"
                      checked={@data_sources_enabled.announcement_smart}
                      disabled={@config_confirmed}
                      class="sr-only peer"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                    </div>
                  </label>
                </div>
              </div>
              
    <!-- LinkedIn Posts -->
              <div
                class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer"),
                  if(@data_sources_enabled.linkedin,
                    do:
                      "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                    else:
                      "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                  ),
                  if(@config_confirmed, do: "opacity-50", else: "")
                ]}
                phx-click={unless @config_confirmed, do: "toggle_data_source"}
                phx-value-source="linkedin"
              >
                <div class="flex items-center">
                  <PC.icon
                    name="hero-user-group"
                    class={[
                      "w-5 h-5 mr-3",
                      if(@data_sources_enabled.linkedin,
                        do: "text-green-600 dark:text-green-400",
                        else: "text-gray-400 dark:text-gray-500"
                      )
                    ]}
                  />
                  <div>
                    <div class={[
                      "font-medium",
                      if(@data_sources_enabled.linkedin,
                        do: "text-gray-800 dark:text-gray-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      LinkedIn Posts
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      Recent company LinkedIn activity
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class={[
                    "relative inline-flex items-center",
                    if(@config_confirmed, do: "cursor-not-allowed", else: "cursor-pointer")
                  ]}>
                    <input
                      type="checkbox"
                      checked={@data_sources_enabled.linkedin}
                      disabled={@config_confirmed}
                      class="sr-only peer"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                    </div>
                  </label>
                </div>
              </div>
              
    <!-- Raw Announcements -->
              <div
                class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  if(@config_confirmed or @agent_options.auto_competitor,
                    do: "cursor-not-allowed",
                    else: "cursor-pointer"
                  ),
                  if(@data_sources_enabled.announcement_raw and not @agent_options.auto_competitor,
                    do:
                      "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                    else:
                      "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                  ),
                  if(@config_confirmed or @agent_options.auto_competitor, do: "opacity-50", else: "")
                ]}
                phx-click={
                  unless @config_confirmed or @agent_options.auto_competitor, do: "toggle_data_source"
                }
                phx-value-source="announcement_raw"
              >
                <div class="flex items-center">
                  <PC.icon
                    name="hero-document-text"
                    class={[
                      "w-5 h-5 mr-3",
                      if(@data_sources_enabled.announcement_raw,
                        do: "text-green-600 dark:text-green-400",
                        else: "text-gray-400 dark:text-gray-500"
                      )
                    ]}
                  />
                  <div>
                    <div class={[
                      "font-medium",
                      if(
                        @data_sources_enabled.announcement_raw and not @agent_options.auto_competitor,
                        do: "text-gray-800 dark:text-gray-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      Raw Announcements
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      <%= if @agent_options.auto_competitor do %>
                        Disabled for auto competitor mode
                      <% else %>
                        Unprocessed announcement data
                      <% end %>
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class={[
                    "relative inline-flex items-center",
                    if(@config_confirmed or @agent_options.auto_competitor,
                      do: "cursor-not-allowed",
                      else: "cursor-pointer"
                    )
                  ]}>
                    <input
                      type="checkbox"
                      checked={
                        @data_sources_enabled.announcement_raw and not @agent_options.auto_competitor
                      }
                      disabled={@config_confirmed or @agent_options.auto_competitor}
                      class="sr-only peer"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
    <!-- Confirm Configuration Button -->
          <div class="flex justify-center">
            <button
              phx-click={unless @config_confirmed, do: "confirm_configuration"}
              disabled={@config_confirmed}
              class={[
                "px-8 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                if(@config_confirmed,
                  do:
                    "bg-gray-400 dark:bg-gray-600 text-gray-200 dark:text-gray-400 cursor-not-allowed",
                  else:
                    "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                )
              ]}
            >
              <%= if @config_confirmed do %>
                <PC.icon name="hero-check-circle" class="w-5 h-5" /> Configuration Confirmed
              <% else %>
                <PC.icon name="hero-check" class="w-5 h-5" /> Confirm Configuration
              <% end %>
            </button>
          </div>
        </div>
        
    <!-- Step 2: Ticker Input (shown after configuration confirmed) -->
        <%= if @config_confirmed and not @is_gathering do %>
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  2
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                  <%= if @agent_options.auto_competitor do %>
                    Enter Your Company Ticker
                  <% else %>
                    Enter Company Tickers
                  <% end %>
                </h2>
              </div>
            </div>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              <%= if @agent_options.auto_competitor do %>
                Enter your company ticker symbol. We'll automatically discover and analyze up to 5 of your main competitors.
              <% else %>
                Enter your company ticker and a competitor's ticker symbol to get today's intelligence update with comparison context.
              <% end %>
            </p>

            <form phx-submit="gather_information" class="space-y-6">
              <div class={
                if @agent_options.auto_competitor,
                  do: "max-w-md mx-auto",
                  else: "grid grid-cols-1 md:grid-cols-2 gap-6"
              }>
                <!-- My Company Ticker -->
                <div>
                  <label
                    for="my_company_ticker"
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                  >
                    My Company Ticker Symbol
                  </label>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 dark:text-gray-400 text-sm font-medium">ASX:</span>
                    </div>
                    <input
                      type="text"
                      id="my_company_ticker"
                      name="my_company_ticker"
                      value={@my_company_ticker}
                      phx-change="update_my_company_ticker"
                      placeholder="e.g., CBA, WBC"
                      class="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <%= unless @agent_options.auto_competitor do %>
                  <!-- Competitor Ticker -->
                  <div>
                    <label
                      for="ticker"
                      class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >
                      Competitor Ticker Symbol
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 dark:text-gray-400 text-sm font-medium">ASX:</span>
                      </div>
                      <input
                        type="text"
                        id="ticker"
                        name="ticker"
                        value={@ticker}
                        phx-change="update_ticker"
                        placeholder="e.g., ANZ, NAB"
                        class="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  </div>
                <% end %>
              </div>

              <div class="flex justify-center">
                <button
                  type="submit"
                  disabled={
                    if @agent_options.auto_competitor do
                      String.trim(@my_company_ticker) == ""
                    else
                      String.trim(@ticker) == "" or String.trim(@my_company_ticker) == ""
                    end
                  }
                  class={[
                    "px-8 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                    if(
                      if @agent_options.auto_competitor do
                        String.trim(@my_company_ticker) == ""
                      else
                        String.trim(@ticker) == "" or String.trim(@my_company_ticker) == ""
                      end,
                      do:
                        "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed",
                      else:
                        "bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    )
                  ]}
                >
                  <PC.icon name="hero-magnifying-glass" class="w-5 h-5" />
                  <%= if @agent_options.auto_competitor do %>
                    Discover Competitors
                  <% else %>
                    Gather Information
                  <% end %>
                </button>
              </div>
            </form>
          </div>
        <% end %>
        
    <!-- Step 2.5: Competitors Display (shown for auto competitor mode after discovery) -->
        <%= if @agent_options.auto_competitor and not Enum.empty?(@competitors) and not @is_gathering do %>
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  2.5
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                  Discovered Competitors
                </h2>
              </div>
            </div>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
              We found {length(@competitors)} competitors for {@my_company_ticker}. Select which ones to include in your analysis.
            </p>
            
    <!-- Debug Mode Toggle -->
            <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <PC.icon name="hero-bug-ant" class="w-5 h-5 mr-3 text-gray-600 dark:text-gray-400" />
                  <div>
                    <div class="font-medium text-gray-900 dark:text-white">
                      Debug Mode
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                      Show data sources for all findings and analysis
                    </div>
                  </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={@debug_mode}
                    phx-click="toggle_debug_mode"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                  </div>
                </label>
              </div>
            </div>
            
    <!-- Competitors Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
              <%= for competitor <- @competitors do %>
                <div
                  class={[
                    "flex items-center justify-between p-3 rounded-lg transition-all duration-300 cursor-pointer",
                    if(Map.get(@competitors_enabled, String.to_atom(competitor), true),
                      do:
                        "bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700",
                      else:
                        "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"
                    )
                  ]}
                  phx-click="toggle_competitor"
                  phx-value-competitor={competitor}
                >
                  <div class="flex items-center">
                    <PC.icon
                      name="hero-building-office"
                      class={[
                        "w-5 h-5 mr-3",
                        if(Map.get(@competitors_enabled, String.to_atom(competitor), true),
                          do: "text-green-600 dark:text-green-400",
                          else: "text-gray-400 dark:text-gray-500"
                        )
                      ]}
                    />
                    <div>
                      <div class={[
                        "font-medium",
                        if(Map.get(@competitors_enabled, String.to_atom(competitor), true),
                          do: "text-gray-800 dark:text-gray-200",
                          else: "text-gray-500 dark:text-gray-400"
                        )
                      ]}>
                        {competitor}
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={Map.get(@competitors_enabled, String.to_atom(competitor), true)}
                        class="sr-only peer"
                      />
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600">
                      </div>
                    </label>
                  </div>
                </div>
              <% end %>
            </div>
            
    <!-- Gather Information Button -->
            <div class="flex justify-center">
              <button
                phx-click="start_multi_competitor_gathering"
                disabled={Enum.all?(@competitors_enabled, fn {_k, v} -> not v end)}
                class={[
                  "px-8 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                  if(Enum.all?(@competitors_enabled, fn {_k, v} -> not v end),
                    do:
                      "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed",
                    else:
                      "bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  )
                ]}
              >
                <PC.icon name="hero-magnifying-glass" class="w-5 h-5" />
                Gather Information for Selected Competitors
              </button>
            </div>
          </div>
        <% end %>
        
    <!-- Step 3: Information Gathering Progress (shown during gathering) -->
        <%= if @is_gathering and @gathering_start_time do %>
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  {if @agent_options.auto_competitor, do: "3", else: "3"}
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                  <%= if @agent_options.auto_competitor do %>
                    Gathering Competitor Information
                  <% else %>
                    Gathering Information
                  <% end %>
                </h2>
              </div>
              <div
                class="text-sm text-gray-500 dark:text-gray-400"
                id="gathering-timer"
                phx-hook="Timer"
                data-start-time={@gathering_start_time}
              >
                <%= if @agent_options.auto_competitor do %>
                  Gathering competitor data... <span class="font-mono">00:00</span>
                <% else %>
                  Gathering data... <span class="font-mono">00:00</span>
                <% end %>
              </div>
            </div>
            
    <!-- Data Sources Progress -->
            <div>
              <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Data Sources
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <!-- My Company Info -->
                <div class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  data_source_classes(
                    @data_sources_status.my_company_info,
                    true
                  )
                ]}>
                  <div class="flex items-center">
                    <PC.icon
                      name="hero-building-office-2"
                      class={[
                        "w-5 h-5 mr-3",
                        data_source_icon_classes(
                          @data_sources_status.my_company_info,
                          true
                        )
                      ]}
                    />
                    <div>
                      <div class={[
                        "font-medium",
                        data_source_text_classes(
                          @data_sources_status.my_company_info,
                          true
                        )
                      ]}>
                        My Company Info
                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                          Required
                        </span>
                      </div>
                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        Fetches your company name and description
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <!-- Status Icon -->
                    <%= case @data_sources_status.my_company_info do %>
                      <% :processing -> %>
                        <PC.icon name="hero-arrow-path" class="w-4 h-4 text-blue-500 animate-spin" />
                      <% :done -> %>
                        <PC.icon name="hero-check-circle" class="w-4 h-4 text-green-500" />
                      <% _ -> %>
                        <div class="w-4 h-4"></div>
                    <% end %>
                  </div>
                </div>
                
    <!-- Basic Company Info -->
                <div class={[
                  "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                  data_source_classes(
                    @data_sources_status.basic_company_info,
                    true
                  )
                ]}>
                  <div class="flex items-center">
                    <PC.icon
                      name="hero-building-library"
                      class={[
                        "w-5 h-5 mr-3",
                        data_source_icon_classes(
                          @data_sources_status.basic_company_info,
                          true
                        )
                      ]}
                    />
                    <div>
                      <div class={[
                        "font-medium",
                        data_source_text_classes(
                          @data_sources_status.basic_company_info,
                          true
                        )
                      ]}>
                        Basic Company Info
                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                          Required
                        </span>
                      </div>
                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        Fetches name, description, LinkedIn URL
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <!-- Status Icon -->
                    <%= case @data_sources_status.basic_company_info do %>
                      <% :processing -> %>
                        <PC.icon name="hero-arrow-path" class="w-4 h-4 text-blue-500 animate-spin" />
                      <% :done -> %>
                        <PC.icon name="hero-check-circle" class="w-4 h-4 text-green-500" />
                      <% _ -> %>
                        <div class="w-4 h-4"></div>
                    <% end %>
                  </div>
                </div>
                
    <!-- Market Data -->
                <%= if @data_sources_enabled.market_data do %>
                  <div class={[
                    "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                    data_source_classes(
                      @data_sources_status.market_data,
                      @data_sources_enabled.market_data
                    )
                  ]}>
                    <div class="flex items-center">
                      <PC.icon
                        name="hero-chart-bar"
                        class={[
                          "w-5 h-5 mr-3",
                          data_source_icon_classes(
                            @data_sources_status.market_data,
                            @data_sources_enabled.market_data
                          )
                        ]}
                      />
                      <div>
                        <div class={[
                          "font-medium",
                          data_source_text_classes(
                            @data_sources_status.market_data,
                            @data_sources_enabled.market_data
                          )
                        ]}>
                          Market Data
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                          Stock prices and trading data
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <!-- Status Icon -->
                      <%= case @data_sources_status.market_data do %>
                        <% :processing -> %>
                          <PC.icon name="hero-arrow-path" class="w-4 h-4 text-blue-500 animate-spin" />
                        <% :done -> %>
                          <PC.icon name="hero-check-circle" class="w-4 h-4 text-green-500" />
                        <% _ -> %>
                          <div class="w-4 h-4"></div>
                      <% end %>
                    </div>
                  </div>
                <% end %>
                
    <!-- Smart Announcements -->
                <%= if @data_sources_enabled.announcement_smart do %>
                  <div class={[
                    "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                    data_source_classes(
                      @data_sources_status.announcement_smart,
                      @data_sources_enabled.announcement_smart
                    )
                  ]}>
                    <div class="flex items-center">
                      <PC.icon
                        name="hero-megaphone"
                        class={[
                          "w-5 h-5 mr-3",
                          data_source_icon_classes(
                            @data_sources_status.announcement_smart,
                            @data_sources_enabled.announcement_smart
                          )
                        ]}
                      />
                      <div>
                        <div class={[
                          "font-medium",
                          data_source_text_classes(
                            @data_sources_status.announcement_smart,
                            @data_sources_enabled.announcement_smart
                          )
                        ]}>
                          Smart Announcements
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                          AI-processed company announcements
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <!-- Status Icon -->
                      <%= case @data_sources_status.announcement_smart do %>
                        <% :processing -> %>
                          <PC.icon name="hero-arrow-path" class="w-4 h-4 text-blue-500 animate-spin" />
                        <% :done -> %>
                          <PC.icon name="hero-check-circle" class="w-4 h-4 text-green-500" />
                        <% _ -> %>
                          <div class="w-4 h-4"></div>
                      <% end %>
                    </div>
                  </div>
                <% end %>
                
    <!-- LinkedIn Data -->
                <%= if @data_sources_enabled.linkedin do %>
                  <div class={[
                    "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                    data_source_classes(@data_sources_status.linkedin, @data_sources_enabled.linkedin)
                  ]}>
                    <div class="flex items-center">
                      <PC.icon
                        name="hero-user-group"
                        class={[
                          "w-5 h-5 mr-3",
                          data_source_icon_classes(
                            @data_sources_status.linkedin,
                            @data_sources_enabled.linkedin
                          )
                        ]}
                      />
                      <div>
                        <div class={[
                          "font-medium",
                          data_source_text_classes(
                            @data_sources_status.linkedin,
                            @data_sources_enabled.linkedin
                          )
                        ]}>
                          LinkedIn Posts
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                          Recent company LinkedIn activity
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <!-- Status Icon -->
                      <%= case @data_sources_status.linkedin do %>
                        <% :processing -> %>
                          <PC.icon name="hero-arrow-path" class="w-4 h-4 text-blue-500 animate-spin" />
                        <% :done -> %>
                          <PC.icon name="hero-check-circle" class="w-4 h-4 text-green-500" />
                        <% _ -> %>
                          <div class="w-4 h-4"></div>
                      <% end %>
                    </div>
                  </div>
                <% end %>
                
    <!-- Raw Announcements -->
                <%= if @data_sources_enabled.announcement_raw do %>
                  <div class={[
                    "flex items-center justify-between p-3 rounded-lg transition-all duration-300",
                    data_source_classes(
                      @data_sources_status.announcement_raw,
                      @data_sources_enabled.announcement_raw
                    )
                  ]}>
                    <div class="flex items-center">
                      <PC.icon
                        name="hero-document-text"
                        class={[
                          "w-5 h-5 mr-3",
                          data_source_icon_classes(
                            @data_sources_status.announcement_raw,
                            @data_sources_enabled.announcement_raw
                          )
                        ]}
                      />
                      <div>
                        <div class={[
                          "font-medium",
                          data_source_text_classes(
                            @data_sources_status.announcement_raw,
                            @data_sources_enabled.announcement_raw
                          )
                        ]}>
                          Raw Announcements
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                          Unprocessed announcement data
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <!-- Status Icon -->
                      <%= case @data_sources_status.announcement_raw do %>
                        <% :processing -> %>
                          <PC.icon name="hero-arrow-path" class="w-4 h-4 text-blue-500 animate-spin" />
                        <% :done -> %>
                          <PC.icon name="hero-check-circle" class="w-4 h-4 text-green-500" />
                        <% _ -> %>
                          <div class="w-4 h-4"></div>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
        <!-- Step 4: System Prompt (shown after gathering) -->
        <%= if @gathered_info do %>
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex items-center mb-4">
              <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                4
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                Generate Daily Update
              </h2>
            </div>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              Review the system prompt and generate your daily competitor intelligence update.
            </p>

            <div class="mb-6">
              <label
                for="system_prompt"
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                System Prompt
              </label>
              <textarea
                id="system_prompt"
                name="system_prompt"
                rows="8"
                value={@system_prompt}
                phx-change="update_system_prompt"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none"
                placeholder="System prompt will be generated automatically..."
              >{@system_prompt}</textarea>
            </div>

            <form phx-submit="generate_research" class="space-y-4">
              <div class="flex justify-center">
                <button
                  type="submit"
                  disabled={
                    @is_generating or is_nil(@system_prompt) or String.trim(@system_prompt) == ""
                  }
                  class={[
                    "px-8 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                    if(@is_generating or is_nil(@system_prompt) or String.trim(@system_prompt) == "",
                      do:
                        "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed",
                      else:
                        "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    )
                  ]}
                >
                  <%= if @is_generating do %>
                    <PC.icon name="hero-arrow-path" class="w-5 h-5 animate-spin" />
                    Generating Report...
                  <% else %>
                    <PC.icon name="hero-document-text" class="w-5 h-5" /> Generate Daily Update
                  <% end %>
                </button>
              </div>
            </form>
          </div>
        <% end %>
        <!-- Step 5: Research Results -->
        <%= if @research_result do %>
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  5
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                  Daily Intelligence Update for {@research_result.ticker}
                </h2>
              </div>
              <!-- Evaluate Button -->
              <button
                phx-click="evaluate_research"
                disabled={@is_evaluating}
                class={[
                  "px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm",
                  if(@is_evaluating,
                    do:
                      "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed",
                    else: "bg-green-600 hover:bg-green-700 text-white shadow-md hover:shadow-lg"
                  )
                ]}
              >
                <%= if @is_evaluating do %>
                  <div class="flex items-center">
                    <PC.icon name="hero-arrow-path" class="w-4 h-4 mr-2 animate-spin" /> Evaluating...
                  </div>
                <% else %>
                  <div class="flex items-center">
                    <PC.icon name="hero-star" class="w-4 h-4 mr-2" /> Evaluate
                  </div>
                <% end %>
              </button>
            </div>

            <div class="prose dark:prose-invert max-w-none">
              {raw(@research_result.content)}
            </div>
            
    <!-- Email Section for Auto Competitor Mode -->
            <%= if @agent_options.auto_competitor do %>
              <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  📧 Send Report via Email
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                  Send this competitor analysis report to an email address.
                </p>

                <form phx-submit="send_email" class="space-y-4">
                  <div>
                    <label
                      for="email_address"
                      class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >
                      Email Address
                    </label>
                    <div class="flex gap-3">
                      <input
                        type="email"
                        id="email_address"
                        name="email_address"
                        value={@email_address}
                        phx-change="update_email_address"
                        placeholder="Enter email address"
                        required
                        class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                      <button
                        type="submit"
                        disabled={@is_sending_email or String.trim(@email_address) == ""}
                        class={[
                          "px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                          if(@is_sending_email or String.trim(@email_address) == "",
                            do:
                              "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed",
                            else:
                              "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                          )
                        ]}
                      >
                        <%= if @is_sending_email do %>
                          <PC.icon name="hero-arrow-path" class="w-5 h-5 animate-spin" /> Sending...
                        <% else %>
                          <PC.icon name="hero-paper-airplane" class="w-5 h-5" /> Send Email
                        <% end %>
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            <% end %>
          </div>
        <% end %>
        <!-- Step 6: Evaluation Results -->
        <%= if @eval_result do %>
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center mb-4">
              <div class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                6
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                Quality Evaluation
              </h2>
            </div>
            
    <!-- Score Display -->
            <div class="mb-6">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Executive Value Score
                </span>
                <span class={[
                  "text-2xl font-bold",
                  cond do
                    @eval_result.score >= 9 -> "text-green-600 dark:text-green-400"
                    @eval_result.score >= 7 -> "text-blue-600 dark:text-blue-400"
                    @eval_result.score >= 5 -> "text-yellow-600 dark:text-yellow-400"
                    true -> "text-red-600 dark:text-red-400"
                  end
                ]}>
                  {@eval_result.score}/10
                </span>
              </div>
              
    <!-- Score Bar -->
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  class={[
                    "h-3 rounded-full transition-all duration-300",
                    cond do
                      @eval_result.score >= 9 -> "bg-green-500"
                      @eval_result.score >= 7 -> "bg-blue-500"
                      @eval_result.score >= 5 -> "bg-yellow-500"
                      true -> "bg-red-500"
                    end
                  ]}
                  style={"width: #{@eval_result.score * 10}%"}
                >
                </div>
              </div>
              
    <!-- Score Interpretation -->
              <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                <%= cond do %>
                  <% @eval_result.score >= 9 -> %>
                    🎯 Exceptional - Critical intelligence that executives must know
                  <% @eval_result.score >= 7 -> %>
                    ✅ Good - Valuable information that executives should be aware of
                  <% @eval_result.score >= 5 -> %>
                    ⚠️ Adequate - Some useful information but limited executive value
                  <% @eval_result.score >= 3 -> %>
                    ❌ Poor - Mostly routine information with little strategic value
                  <% true -> %>
                    🚫 Very Poor - No significant value, waste of executive time
                <% end %>
              </div>
            </div>
            
    <!-- Reasoning -->
            <div class="mb-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Evaluation Reasoning
              </h3>
              <p class="text-gray-700 dark:text-gray-300">{@eval_result.reasoning}</p>
            </div>
            
    <!-- Feedback -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Improvement Feedback
              </h3>
              <p class="text-gray-700 dark:text-gray-300">{@eval_result.feedback}</p>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("update_ticker", %{"ticker" => ticker}, socket) do
    {:noreply, assign(socket, :ticker, String.upcase(ticker))}
  end

  @impl true
  def handle_event("update_my_company_ticker", %{"my_company_ticker" => my_company_ticker}, socket) do
    {:noreply, assign(socket, :my_company_ticker, String.upcase(my_company_ticker))}
  end

  @impl true
  def handle_event("gather_information", params, socket) do
    if socket.assigns.agent_options.auto_competitor do
      handle_auto_competitor_discovery(params, socket)
    else
      handle_regular_gather_information(params, socket)
    end
  end

  @impl true
  def handle_event("generate_research", _params, socket) do
    # Use the system prompt to generate the report with LangChain
    send(self(), {:generate_research, socket.assigns.system_prompt, socket.assigns.ticker})

    {:noreply,
     socket
     |> assign(:is_generating, true)
     |> assign(:research_result, nil)
     |> assign(:eval_result, nil)}
  end

  @impl true
  def handle_event("evaluate_research", _params, socket) do
    # Start evaluation of the research result
    send(
      self(),
      {:evaluate_research, socket.assigns.research_result, socket.assigns.ticker, socket.assigns.my_company_ticker,
       socket.assigns.gathered_info}
    )

    {:noreply,
     socket
     |> assign(:is_evaluating, true)
     |> assign(:eval_result, nil)}
  end

  @impl true
  def handle_event("update_system_prompt", %{"value" => system_prompt}, socket) do
    {:noreply,
     socket
     |> assign(:system_prompt, system_prompt)
     |> assign(:system_prompt_edited, true)}
  end

  @impl true
  def handle_event("toggle_competitor", %{"competitor" => competitor}, socket) do
    competitor_atom = String.to_atom(competitor)
    current_enabled = Map.get(socket.assigns.competitors_enabled, competitor_atom, true)

    updated_enabled =
      Map.put(socket.assigns.competitors_enabled, competitor_atom, not current_enabled)

    {:noreply, assign(socket, :competitors_enabled, updated_enabled)}
  end

  @impl true
  def handle_event("start_multi_competitor_gathering", _params, socket) do
    # Get enabled competitors
    enabled_competitors =
      socket.assigns.competitors_enabled
      |> Enum.filter(fn {_k, v} -> v end)
      |> Enum.map(fn {k, _v} -> Atom.to_string(k) end)

    if Enum.empty?(enabled_competitors) do
      {:noreply, socket}
      # Start gathering for multiple competitors
    else
      send(
        self(),
        {:gather_multi_competitor_information, enabled_competitors, socket.assigns.my_company_ticker}
      )

      {:noreply,
       socket
       |> assign(:is_gathering, true)
       |> assign(:gathering_start_time, System.system_time(:millisecond))}
    end
  end

  @impl true
  def handle_event("toggle_data_source", %{"source" => source}, socket) do
    # Only allow changes if configuration is not confirmed
    if socket.assigns.config_confirmed do
      {:noreply, socket}
    else
      source_atom = String.to_existing_atom(source)
      current_enabled = socket.assigns.data_sources_enabled
      updated_enabled = Map.put(current_enabled, source_atom, !current_enabled[source_atom])

      {:noreply, assign(socket, :data_sources_enabled, updated_enabled)}
    end
  end

  @impl true
  def handle_event("toggle_agent_option", %{"option" => option}, socket) do
    option_atom = String.to_existing_atom(option)

    # For agent options, we want exclusive selection (only one can be true at a time)
    updated_options =
      case option_atom do
        :bullet_point_description ->
          %{
            bullet_point_description: true,
            report_what_matters: false,
            auto_competitor: false,
            chain_of_thought: false
          }

        :report_what_matters ->
          %{
            bullet_point_description: false,
            report_what_matters: true,
            auto_competitor: false,
            chain_of_thought: false
          }

        :auto_competitor ->
          %{
            bullet_point_description: false,
            report_what_matters: false,
            auto_competitor: true,
            chain_of_thought: false
          }
      end

    # Only allow changes if configuration is not confirmed
    if socket.assigns.config_confirmed do
      {:noreply, socket}
    else
      # If auto_competitor is selected, disable announcement_raw
      updated_data_sources =
        if option_atom == :auto_competitor do
          Map.put(socket.assigns.data_sources_enabled, :announcement_raw, false)
        else
          socket.assigns.data_sources_enabled
        end

      {:noreply,
       socket
       |> assign(:agent_options, updated_options)
       |> assign(:data_sources_enabled, updated_data_sources)}
    end
  end

  @impl true
  def handle_event("confirm_configuration", _params, socket) do
    {:noreply, assign(socket, :config_confirmed, true)}
  end

  @impl true
  def handle_event("toggle_debug_mode", _params, socket) do
    {:noreply, assign(socket, :debug_mode, !socket.assigns.debug_mode)}
  end

  @impl true
  def handle_event("update_email_address", %{"email_address" => email_address}, socket) do
    {:noreply, assign(socket, :email_address, email_address)}
  end

  @impl true
  def handle_event("send_email", %{"email_address" => email_address}, socket) do
    if socket.assigns.research_result && socket.assigns.agent_options.auto_competitor do
      # Start async email sending
      send(
        self(),
        {:send_competitor_email, email_address, socket.assigns.research_result, socket.assigns.my_company_ticker}
      )

      {:noreply,
       socket
       |> assign(:is_sending_email, true)
       |> assign(:email_address, email_address)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("reset_page", _params, socket) do
    # Reset all state to initial values (same as mount)
    {:noreply,
     socket
     |> assign(:config_confirmed, false)
     |> assign(:ticker, "")
     |> assign(:my_company_ticker, "")
     |> assign(:is_gathering, false)
     |> assign(:is_generating, false)
     |> assign(:gathered_info, nil)
     |> assign(:system_prompt, nil)
     |> assign(:system_prompt_edited, false)
     |> assign(:research_result, nil)
     |> assign(:eval_result, nil)
     |> assign(:is_evaluating, false)
     |> assign(:market_data, nil)
     |> assign(:company_info, nil)
     |> assign(:company_name, nil)
     |> assign(:my_company_info, nil)
     |> assign(:announcement_smart_data, nil)
     |> assign(:announcement_raw_data, nil)
     |> assign(:linkedin_data, nil)
     |> assign(:gathering_start_time, nil)
     |> assign(:data_sources_status, %{
       my_company_info: :idle,
       basic_company_info: :idle,
       market_data: :idle,
       announcement_smart: :idle,
       announcement_raw: :idle,
       linkedin: :idle,
       hotcopper: :disabled,
       previous_answer_reg: :disabled
     })
     |> assign(:data_sources_enabled, %{
       my_company_info: true,
       basic_company_info: true,
       market_data: true,
       announcement_smart: true,
       announcement_raw: false,
       linkedin: true,
       hotcopper: false,
       previous_answer_reg: false
     })
     |> assign(:agent_options, %{
       bullet_point_description: false,
       report_what_matters: false,
       auto_competitor: true,
       chain_of_thought: false
     })
     |> assign(:competitors, [])
     |> assign(:competitors_enabled, %{})
     |> assign(:debug_mode, false)
     |> assign(:email_address, "")
     |> assign(:is_sending_email, false)}
  end

  # Private helper functions for handle_event clauses

  defp handle_auto_competitor_discovery(%{"my_company_ticker" => my_company_ticker}, socket) do
    # Only allow gathering if configuration is confirmed
    if socket.assigns.config_confirmed do
      my_company_ticker = String.upcase(String.trim(my_company_ticker))

      if my_company_ticker == "" do
        {:noreply, socket}
        # Discover competitors for the given ticker
      else
        send(self(), {:discover_competitors, my_company_ticker})

        {:noreply,
         socket
         |> assign(:is_gathering, true)
         |> assign(:my_company_ticker, my_company_ticker)
         |> assign(:gathering_start_time, System.system_time(:millisecond))}
      end
    else
      {:noreply, socket}
    end
  end

  defp handle_regular_gather_information(%{"ticker" => ticker, "my_company_ticker" => my_company_ticker}, socket) do
    # Only allow gathering if configuration is confirmed
    if socket.assigns.config_confirmed do
      ticker = String.trim(String.upcase(ticker))
      my_company_ticker = String.trim(String.upcase(my_company_ticker))

      if ticker != "" and my_company_ticker != "" do
        # Reset data sources status and start timer
        current_time = System.system_time(:millisecond)

        # Call the actual Company Research Agent
        send(self(), {:gather_information, ticker, my_company_ticker})

        {:noreply,
         socket
         |> assign(:is_gathering, true)
         |> assign(:gathered_info, nil)
         |> assign(:market_data, nil)
         |> assign(:company_info, nil)
         |> assign(:company_name, nil)
         |> assign(:my_company_info, nil)
         |> assign(:announcement_smart_data, nil)
         |> assign(:announcement_raw_data, nil)
         |> assign(:linkedin_data, nil)
         |> assign(:gathering_start_time, current_time)
         |> assign(:data_sources_status, %{
           my_company_info: :idle,
           basic_company_info: :idle,
           market_data: :idle,
           announcement_smart: :idle,
           announcement_raw: :idle,
           linkedin: :idle,
           hotcopper: :disabled,
           previous_answer_reg: :disabled
         })
         # Only reset system_prompt and system_prompt_edited if user hasn't manually edited
         |> then(fn socket ->
           if socket.assigns.system_prompt_edited do
             socket
           else
             socket
             |> assign(:system_prompt, nil)
             |> assign(:system_prompt_edited, false)
           end
         end)}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:discover_competitors, my_company_ticker}, socket) do
    # Start async competitor discovery process
    liveview_pid = self()

    Task.start(fn ->
      # Try to get existing competitors first
      competitors = Gaia.Markets.get_all_competitors_by_ticker("asx", my_company_ticker)

      if Enum.empty?(competitors) do
        # No existing competitors, try to create them from Yahoo
        case Gaia.Markets.create_competitors_from_yahoo(my_company_ticker, "asx") do
          {:ok, _competitor_records} ->
            # Get the newly created competitors
            new_competitors = Gaia.Markets.get_all_competitors_by_ticker("asx", my_company_ticker)
            send(liveview_pid, {:competitors_discovered, new_competitors})

          {:error, _reason} ->
            send(liveview_pid, {:competitors_discovery_failed, "Failed to discover competitors"})
        end
      else
        # Use existing competitors
        send(liveview_pid, {:competitors_discovered, competitors})
      end
    end)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:competitors_discovered, competitors}, socket) do
    # Initialize competitors_enabled map with all competitors enabled by default
    competitors_enabled = Map.new(competitors, &{String.to_atom(&1), true})

    {:noreply,
     socket
     |> assign(:is_gathering, false)
     |> assign(:competitors, competitors)
     |> assign(:competitors_enabled, competitors_enabled)}
  end

  @impl true
  def handle_info({:competitors_discovery_failed, reason}, socket) do
    {:noreply,
     socket
     |> assign(:is_gathering, false)
     |> put_flash(:error, "Failed to discover competitors: #{reason}")}
  end

  @impl true
  def handle_info({:gather_multi_competitor_information, competitor_tickers, my_company_ticker}, socket) do
    # Start async gathering process for multiple competitors
    liveview_pid = self()
    enabled_sources = socket.assigns.data_sources_enabled

    Task.start(fn ->
      CompanyResearchAgent.start_multi_competitor_gathering(
        competitor_tickers,
        my_company_ticker,
        liveview_pid,
        enabled_sources
      )
    end)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:competitor_data_warning, ticker, reason}, socket) do
    # Log competitor data warnings but don't stop the process
    {:noreply, put_flash(socket, :warning, "Warning: Could not gather data for #{ticker} - #{reason}")}
  end

  @impl true
  def handle_info({:multi_competitor_gathering_failed, reason}, socket) do
    {:noreply,
     socket
     |> assign(:is_gathering, false)
     |> put_flash(:error, "Failed to gather competitor data: #{reason}")}
  end

  @impl true
  def handle_info({:multi_competitor_data_ready, compiled_data}, socket) do
    # Compile the gathered multi-competitor data and generate the response
    compile_and_update_socket_multi_competitor(socket, compiled_data)
  end

  @impl true
  def handle_info({:gather_information, ticker, my_company_ticker}, socket) do
    # Start async gathering process - capture the LiveView PID before starting the task
    liveview_pid = self()
    enabled_sources = socket.assigns.data_sources_enabled

    Task.start(fn ->
      CompanyResearchAgent.start_gathering(
        ticker,
        my_company_ticker,
        liveview_pid,
        enabled_sources
      )
    end)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:data_source_update, source, status}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, source, status)

    {:noreply, assign(socket, :data_sources_status, updated_status)}
  end

  @impl true
  def handle_info({:data_source_complete, :my_company_info, my_company_info}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, :my_company_info, :done)

    socket =
      socket
      |> assign(:data_sources_status, updated_status)
      |> assign(:my_company_info, my_company_info)

    # Check if we have all data to compile
    {:noreply, check_and_compile_data(socket)}
  end

  @impl true
  def handle_info({:data_source_complete, :basic_company_info, basic_company_info}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, :basic_company_info, :done)

    socket =
      socket
      |> assign(:data_sources_status, updated_status)
      |> assign(:basic_company_info, basic_company_info)

    # Check if we have all data to compile
    {:noreply, check_and_compile_data(socket)}
  end

  @impl true
  def handle_info({:data_source_complete, :market_data, timeseries_data}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, :market_data, :done)

    socket =
      socket
      |> assign(:data_sources_status, updated_status)
      |> assign(:market_data, timeseries_data)

    # Check if we have all data to compile
    {:noreply, check_and_compile_data(socket)}
  end

  @impl true
  def handle_info({:data_source_complete, :announcement_smart, announcement_smart_data}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, :announcement_smart, :done)

    socket =
      socket
      |> assign(:data_sources_status, updated_status)
      |> assign(:announcement_smart_data, announcement_smart_data)

    # Check if we have all data to compile
    {:noreply, check_and_compile_data(socket)}
  end

  @impl true
  def handle_info({:data_source_complete, :announcement_raw, announcement_raw_data}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, :announcement_raw, :done)

    socket =
      socket
      |> assign(:data_sources_status, updated_status)
      |> assign(:announcement_raw_data, announcement_raw_data)

    # Check if we have all data to compile
    {:noreply, check_and_compile_data(socket)}
  end

  @impl true
  def handle_info({:data_source_complete, :linkedin, linkedin_data}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, :linkedin, :done)

    socket =
      socket
      |> assign(:data_sources_status, updated_status)
      |> assign(:linkedin_data, linkedin_data)

    # Check if we have all data to compile
    {:noreply, check_and_compile_data(socket)}
  end

  @impl true
  def handle_info({:data_source_error, source, reason}, socket) do
    current_status = socket.assigns.data_sources_status
    updated_status = Map.put(current_status, source, :idle)

    {:noreply,
     socket
     |> assign(:data_sources_status, updated_status)
     |> put_flash(:error, "Failed to fetch #{source}: #{inspect(reason)}")}
  end

  @impl true
  def handle_info({:generate_research, system_prompt, ticker}, socket) do
    case CompanyResearchAgent.generate_research_report(system_prompt, ticker) do
      {:ok, content} ->
        research_result = %{
          ticker: ticker,
          content: content
        }

        {:noreply,
         socket
         |> assign(:is_generating, false)
         |> assign(:research_result, research_result)}

      {:error, reason} ->
        {:noreply,
         socket
         |> assign(:is_generating, false)
         |> put_flash(:error, "Failed to generate research report: #{reason}")}
    end
  end

  @impl true
  def handle_info({:evaluate_research, research_result, ticker, my_company_ticker, gathered_info}, socket) do
    case HadesWeb.Agents.EvalAgent.evaluate_research_result(
           research_result.content,
           ticker,
           my_company_ticker,
           gathered_info
         ) do
      {:ok, eval_result} ->
        {:noreply,
         socket
         |> assign(:is_evaluating, false)
         |> assign(:eval_result, eval_result)}

      {:error, reason} ->
        {:noreply,
         socket
         |> assign(:is_evaluating, false)
         |> put_flash(:error, "Failed to evaluate research: #{reason}")}
    end
  end

  @impl true
  def handle_info({:send_competitor_email, email_address, research_result, my_company_ticker}, socket) do
    # Send the email using the same functionality as the deleted worker
    Gaia.Notifications.Email.deliver(
      EmailTransactional.Operations,
      :competitor_analysis_daily_update,
      [research_result.content, my_company_ticker, email_address]
    )

    {:noreply,
     socket
     |> assign(:is_sending_email, false)
     |> put_flash(:info, "Email sent successfully to #{email_address}")}
  rescue
    error ->
      {:noreply,
       socket
       |> assign(:is_sending_email, false)
       |> put_flash(:error, "Failed to send email: #{inspect(error)}")}
  end

  # Helper functions for data source styling
  defp data_source_classes(status, enabled)
  # Available and enabled - bright, clear background
  defp data_source_classes(:idle, true), do: "bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700"

  # Available but disabled - muted background
  defp data_source_classes(:idle, false),
    do: "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 opacity-60"

  defp data_source_classes(:processing, _),
    do: "bg-blue-100 dark:bg-blue-900/40 border-2 border-blue-300 dark:border-blue-600 shadow-sm"

  defp data_source_classes(:done, _),
    do: "bg-green-100 dark:bg-green-900/40 border-2 border-green-300 dark:border-green-600 shadow-sm"

  # Coming soon - very muted
  defp data_source_classes(:disabled, _),
    do: "bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 opacity-40"

  defp data_source_icon_classes(status, enabled)
  # Available and enabled - more prominent
  defp data_source_icon_classes(:idle, true), do: "text-blue-600 dark:text-blue-400"
  # Available but disabled - muted
  defp data_source_icon_classes(:idle, false), do: "text-gray-400 dark:text-gray-500"
  defp data_source_icon_classes(:processing, _), do: "text-blue-600 dark:text-blue-400"
  defp data_source_icon_classes(:done, _), do: "text-green-600 dark:text-green-400"
  # Coming soon - very muted
  defp data_source_icon_classes(:disabled, _), do: "text-gray-300 dark:text-gray-600"

  defp data_source_text_classes(status, enabled)
  # Available and enabled - clear, readable text
  defp data_source_text_classes(:idle, true), do: "text-gray-800 dark:text-gray-200"
  # Available but disabled - muted text
  defp data_source_text_classes(:idle, false), do: "text-gray-500 dark:text-gray-400"
  defp data_source_text_classes(:processing, _), do: "text-blue-800 dark:text-blue-200"
  defp data_source_text_classes(:done, _), do: "text-green-800 dark:text-green-200"
  # Coming soon - very muted
  defp data_source_text_classes(:disabled, _), do: "text-gray-400 dark:text-gray-600"

  # Helper function to check if all required data is available and compile
  defp check_and_compile_data(socket) do
    data_assignments = extract_data_assignments(socket)

    if all_enabled_sources_ready?(data_assignments) do
      compile_and_update_socket(socket, data_assignments)
    else
      socket
    end
  end

  # Extract data assignments from socket
  defp extract_data_assignments(socket) do
    %{
      my_company_info: socket.assigns[:my_company_info],
      basic_company_info: socket.assigns[:basic_company_info],
      market_data: socket.assigns.market_data,
      announcement_smart_data: socket.assigns.announcement_smart_data,
      announcement_raw_data: socket.assigns.announcement_raw_data,
      linkedin_data: socket.assigns.linkedin_data,
      ticker: socket.assigns.ticker,
      my_company_ticker: socket.assigns.my_company_ticker,
      enabled_sources: socket.assigns.data_sources_enabled
    }
  end

  # Check if all enabled data sources are ready
  defp all_enabled_sources_ready?(%{enabled_sources: enabled_sources} = data) do
    # Both my company info and basic company info are always required and should be ready
    my_company_info_ready = data.my_company_info != nil
    basic_company_info_ready = data.basic_company_info != nil

    data_source_checks = [
      {:market_data, data.market_data},
      {:announcement_smart, data.announcement_smart_data},
      {:announcement_raw, data.announcement_raw_data},
      {:linkedin, data.linkedin_data}
    ]

    my_company_info_ready && basic_company_info_ready &&
      Enum.all?(data_source_checks, fn {source_key, data_value} ->
        source_disabled_or_data_ready?(enabled_sources, source_key, data_value)
      end)
  end

  # Helper function to check if a data source is disabled or has data ready
  defp source_disabled_or_data_ready?(enabled_sources, source_key, data_value) do
    !Map.get(enabled_sources, source_key, false) || data_value != nil
  end

  # Compile data and update socket
  defp compile_and_update_socket(socket, data) do
    # Determine agent type based on current selection
    agent_type =
      cond do
        socket.assigns.agent_options.auto_competitor -> :auto_competitor
        socket.assigns.agent_options.report_what_matters -> :report_what_matters
        true -> :bullet_point_description
      end

    {:ok, %{gathered_info: gathered_info, system_prompt: system_prompt}} =
      CompanyResearchAgent.compile_gathered_info(%{
        ticker: data.ticker,
        my_company_ticker: data.my_company_ticker,
        my_company_info: data.my_company_info,
        basic_company_info: data.basic_company_info,
        market_data: data.market_data,
        announcement_smart_data: data.announcement_smart_data,
        announcement_raw_data: data.announcement_raw_data,
        linkedin_data: data.linkedin_data,
        agent_type: agent_type,
        debug_mode: socket.assigns.debug_mode
      })

    # Only update system prompt if it hasn't been manually edited
    socket = maybe_update_system_prompt(socket, system_prompt)

    socket
    |> assign(:is_gathering, false)
    |> assign(:gathering_start_time, nil)
    |> assign(:gathered_info, gathered_info)
  end

  # Update system prompt only if it hasn't been manually edited
  defp maybe_update_system_prompt(socket, system_prompt) do
    if socket.assigns.system_prompt_edited do
      socket
    else
      assign(socket, :system_prompt, system_prompt)
    end
  end

  # Compile multi-competitor data and update socket
  defp compile_and_update_socket_multi_competitor(socket, compiled_data) do
    # Get my company information
    case Gaia.Markets.get_or_insert_company_information("asx", compiled_data.my_company_ticker) do
      {:ok, my_company_info} ->
        {:ok, %{gathered_info: gathered_info, system_prompt: system_prompt}} =
          CompanyResearchAgent.compile_gathered_info(%{
            ticker: "MULTI_COMPETITOR",
            my_company_ticker: compiled_data.my_company_ticker,
            my_company_info: my_company_info,
            basic_company_info: nil,
            market_data: compiled_data.financial_data,
            announcement_smart_data: compiled_data.announcements,
            announcement_raw_data: nil,
            linkedin_data: compiled_data.linkedin_posts,
            agent_type: :auto_competitor,
            business_summary: compiled_data.business_summary,
            debug_mode: socket.assigns.debug_mode
          })

        # Only update system prompt if it hasn't been manually edited
        socket = maybe_update_system_prompt(socket, system_prompt)

        {:noreply,
         socket
         |> assign(:is_gathering, false)
         |> assign(:gathering_start_time, nil)
         |> assign(:gathered_info, gathered_info)
         |> assign(:ticker, "#{compiled_data.competitor_count} COMPETITORS")
         |> put_flash(
           :info,
           "Successfully analyzed #{compiled_data.competitor_count} competitors"
         )}

      {:error, _reason} ->
        {:noreply,
         socket
         |> assign(:is_gathering, false)
         |> put_flash(
           :error,
           "Failed to get company information for #{compiled_data.my_company_ticker}"
         )}
    end
  end
end
