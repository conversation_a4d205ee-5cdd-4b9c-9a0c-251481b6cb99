defmodule HadesWeb.Agents.CompanyResearchAgent do
  @moduledoc """
  Company Research Agent - Specialized AI agent for company research and analysis
  """

  alias HadesWeb.Agents.CompanyResearchHelper.Announcements
  alias HadesWeb.Agents.CompanyResearchHelper.LinkedIn
  alias HadesWeb.Agents.CompanyResearchHelper.MarketData
  alias LangChain.Chains.LLMChain
  alias LangChain.ChatModels.ChatOpenAI
  alias LangChain.Message

  @doc """
  Start gathering information for multiple competitor tickers and my company ticker asynchronously
  """
  def start_multi_competitor_gathering(competitor_tickers, my_company_ticker, parent_pid, enabled_sources \\ %{}) do
    # First, get my company information
    case get_my_company_information(my_company_ticker, parent_pid) do
      {:ok, _my_company_info} ->
        # Gather information for each competitor and collect all data
        all_competitor_data =
          competitor_tickers
          |> Enum.map(fn ticker ->
            try do
              # Get basic company information for each competitor
              case get_basic_company_information(ticker, parent_pid) do
                {:ok, _company_info} ->
                  # Gather data for this competitor without sending individual completion messages
                  competitor_info = gather_competitor_data(ticker, enabled_sources)
                  {ticker, competitor_info}

                {:error, reason} ->
                  # Log the error but continue with other competitors
                  send(parent_pid, {:competitor_data_warning, ticker, reason})
                  nil
              end
            rescue
              error ->
                # Handle any unexpected errors and continue processing
                send(
                  parent_pid,
                  {:competitor_data_warning, ticker, "Unexpected error: #{inspect(error)}"}
                )

                nil
            end
          end)
          |> Enum.filter(&(&1 != nil))

        # Send completion message with all competitor data
        if Enum.empty?(all_competitor_data) do
          send(
            parent_pid,
            {:multi_competitor_gathering_failed, "No valid competitors found - all tickers may be invalid"}
          )
        else
          # Compile all competitor data into a single response
          compiled_data = compile_multi_competitor_data(all_competitor_data, my_company_ticker)
          send(parent_pid, {:multi_competitor_data_ready, compiled_data})
        end

      {:error, reason} ->
        send(parent_pid, {:data_source_error, :my_company_info, reason})
    end
  end

  # Gather data for a single competitor without sending individual completion messages
  defp gather_competitor_data(ticker, enabled_sources) do
    ticker
    |> initialize_competitor_data()
    |> gather_company_information(ticker)
    |> gather_financial_data_if_enabled(ticker, enabled_sources)
    |> gather_announcements_if_enabled(ticker, enabled_sources)
    |> gather_linkedin_posts_if_enabled(ticker, enabled_sources)
  end

  # Initialize the base competitor data structure
  defp initialize_competitor_data(ticker) do
    %{
      ticker: ticker,
      company_name: nil,
      business_summary: nil,
      financial_data: nil,
      announcements: nil,
      linkedin_posts: nil
    }
  end

  # Gather basic company information and business summary
  defp gather_company_information(competitor_data, ticker) do
    case Gaia.Markets.get_or_insert_company_information("asx", ticker) do
      {:ok, company_info} ->
        business_summary = format_business_summary(company_info)
        company_name = company_info.name || String.upcase(ticker)

        competitor_data
        |> Map.put(:business_summary, business_summary)
        |> Map.put(:company_name, company_name)

      _ ->
        Map.put(competitor_data, :company_name, String.upcase(ticker))
    end
  end

  # Gather financial data if enabled in sources
  defp gather_financial_data_if_enabled(competitor_data, ticker, enabled_sources) do
    if Map.get(enabled_sources, :market_data, false) do
      case gather_market_data_sync(ticker) do
        {:ok, financial_data} ->
          Map.put(competitor_data, :financial_data, financial_data)

        _ ->
          competitor_data
      end
    else
      competitor_data
    end
  end

  # Gather announcements if enabled in sources
  defp gather_announcements_if_enabled(competitor_data, ticker, enabled_sources) do
    if Map.get(enabled_sources, :announcement_smart, false) do
      case gather_announcements_sync(ticker) do
        {:ok, announcements} ->
          Map.put(competitor_data, :announcements, announcements)

        _ ->
          competitor_data
      end
    else
      competitor_data
    end
  end

  # Gather LinkedIn posts if enabled in sources
  defp gather_linkedin_posts_if_enabled(competitor_data, ticker, enabled_sources) do
    if Map.get(enabled_sources, :linkedin_posts, false) do
      case gather_linkedin_posts_sync(ticker) do
        {:ok, linkedin_posts} ->
          Map.put(competitor_data, :linkedin_posts, linkedin_posts)
      end
    else
      competitor_data
    end
  end

  # Compile data from multiple competitors into a single structure
  defp compile_multi_competitor_data(all_competitor_data, my_company_ticker) do
    %{
      business_summary: combine_business_summaries(all_competitor_data),
      financial_data: combine_financial_data(all_competitor_data),
      announcements: combine_announcements(all_competitor_data),
      linkedin_posts: combine_linkedin_posts(all_competitor_data),
      my_company_ticker: my_company_ticker,
      competitor_count: length(all_competitor_data)
    }
  end

  # Combine business summaries from all competitors
  defp combine_business_summaries(all_competitor_data) do
    combined =
      all_competitor_data
      |> Enum.map(fn {_ticker, data} -> data.business_summary end)
      |> Enum.filter(&(&1 != nil))
      |> Enum.join("\n\n")

    if combined == "", do: nil, else: combined
  end

  # Combine financial data from all competitors with company names
  defp combine_financial_data(all_competitor_data) do
    combined =
      all_competitor_data
      |> Enum.map(fn {_ticker, data} ->
        format_competitor_section(data, :financial_data, "")
      end)
      |> Enum.filter(&(&1 != nil))
      |> Enum.join("\n\n")

    if combined == "", do: nil, else: combined
  end

  # Combine announcements from all competitors with company names
  defp combine_announcements(all_competitor_data) do
    combined =
      all_competitor_data
      |> Enum.map(fn {_ticker, data} ->
        format_competitor_section(data, :announcements, " Announcements")
      end)
      |> Enum.filter(&(&1 != nil))
      |> Enum.join("\n\n")

    if combined == "", do: nil, else: combined
  end

  # Combine LinkedIn posts from all competitors with company names
  defp combine_linkedin_posts(all_competitor_data) do
    combined =
      all_competitor_data
      |> Enum.map(fn {_ticker, data} ->
        format_competitor_section(data, :linkedin_posts, " LinkedIn Activity")
      end)
      |> Enum.filter(&(&1 != nil))
      |> Enum.join("\n\n")

    if combined == "", do: nil, else: combined
  end

  # Format a section of competitor data with company name prefix
  defp format_competitor_section(data, field, suffix) do
    field_data = Map.get(data, field)

    if field_data do
      company_name = data.company_name || "Unknown Company"
      "#{company_name}#{suffix}:\n#{field_data}"
    end
  end

  # Synchronous data gathering functions for multi-competitor mode
  defp gather_market_data_sync(ticker) do
    case HadesWeb.Agents.CompanyResearchHelper.MarketData.get_timeseries_data(ticker) do
      {:ok, raw_data} ->
        # Format the raw data properly
        formatted_data = format_market_data(raw_data)
        {:ok, formatted_data}

      {:error, _reason} ->
        {:error, :market_data_failed}
    end
  end

  defp gather_announcements_sync(ticker) do
    case HadesWeb.Agents.CompanyResearchHelper.Announcements.get_announcements_smart_data(ticker) do
      {:ok, raw_data} ->
        # Format the raw data properly using the Announcements helper
        formatted_data =
          HadesWeb.Agents.CompanyResearchHelper.Announcements.format_announcements_summary(raw_data)

        {:ok, formatted_data}

      {:error, _reason} ->
        {:error, :announcements_failed}
    end
  end

  defp gather_linkedin_posts_sync(ticker) do
    case HadesWeb.Agents.CompanyResearchHelper.LinkedIn.get_linkedin_data(ticker) do
      {:ok, raw_data} ->
        # Format the raw data properly using the LinkedIn helper
        formatted_data =
          HadesWeb.Agents.CompanyResearchHelper.LinkedIn.format_linkedin_summary(raw_data)

        {:ok, formatted_data || "No LinkedIn data available"}
    end
  end

  # Helper functions to format raw data
  defp format_market_data(raw_data) when is_list(raw_data) do
    # Format market data similar to how it's done in the regular flow
    HadesWeb.Agents.CompanyResearchHelper.MarketData.format_timeseries_summary(raw_data)
  end

  defp format_business_summary(company_info) do
    name = company_info.name || "Company name not available"
    description = company_info.description || "Company description not available"
    "#{name} is #{description}"
  end

  @doc """
  Start gathering information for a given ticker symbol and my company ticker asynchronously
  """
  def start_gathering(ticker, my_company_ticker, parent_pid, enabled_sources \\ %{}) do
    # First, get my company information
    case get_my_company_information(my_company_ticker, parent_pid) do
      {:ok, _my_company_info} ->
        # Then get basic company information to populate the database
        # This ensures all subsequent calls have access to complete company data
        case get_basic_company_information(ticker, parent_pid) do
          {:ok, _company_info} ->
            # Start only enabled data sources asynchronously
            tasks = start_data_source_tasks(ticker, parent_pid, enabled_sources)

            # Wait for and process results for enabled sources
            await_and_process_results(tasks, parent_pid)

          {:error, reason} ->
            send(parent_pid, {:data_source_error, :basic_company_info, reason})
        end

      {:error, reason} ->
        send(parent_pid, {:data_source_error, :my_company_info, reason})
    end
  end

  # Get my company information first
  defp get_my_company_information(my_company_ticker, parent_pid) do
    send(parent_pid, {:data_source_update, :my_company_info, :processing})

    case Gaia.Markets.get_or_insert_company_information("asx", my_company_ticker) do
      {:ok, my_company_info} ->
        send(parent_pid, {:data_source_complete, :my_company_info, my_company_info})
        {:ok, my_company_info}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # Get basic company information first to populate the database
  defp get_basic_company_information(ticker, parent_pid) do
    send(parent_pid, {:data_source_update, :basic_company_info, :processing})

    case Gaia.Markets.get_or_insert_company_information("asx", ticker) do
      {:ok, company_info} ->
        send(parent_pid, {:data_source_complete, :basic_company_info, company_info})
        {:ok, company_info}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # Start async tasks for enabled data sources
  defp start_data_source_tasks(ticker, parent_pid, enabled_sources) do
    %{}
    |> maybe_start_market_data_task(ticker, parent_pid, enabled_sources)
    |> maybe_start_announcement_smart_task(ticker, parent_pid, enabled_sources)
    |> maybe_start_announcement_raw_task(ticker, parent_pid, enabled_sources)
    |> maybe_start_linkedin_task(ticker, parent_pid, enabled_sources)
  end

  defp maybe_start_market_data_task(tasks, ticker, parent_pid, enabled_sources) do
    if Map.get(enabled_sources, :market_data, true) do
      send(parent_pid, {:data_source_update, :market_data, :processing})
      Map.put(tasks, :market_data, Task.async(fn -> MarketData.get_timeseries_data(ticker) end))
    else
      tasks
    end
  end

  defp maybe_start_announcement_smart_task(tasks, ticker, parent_pid, enabled_sources) do
    if Map.get(enabled_sources, :announcement_smart, true) do
      send(parent_pid, {:data_source_update, :announcement_smart, :processing})

      Map.put(
        tasks,
        :announcement_smart,
        Task.async(fn -> Announcements.get_announcements_smart_data(ticker) end)
      )
    else
      tasks
    end
  end

  defp maybe_start_announcement_raw_task(tasks, ticker, parent_pid, enabled_sources) do
    if Map.get(enabled_sources, :announcement_raw, false) do
      send(parent_pid, {:data_source_update, :announcement_raw, :processing})

      Map.put(
        tasks,
        :announcement_raw,
        Task.async(fn -> Announcements.get_announcements_raw_data(ticker) end)
      )
    else
      tasks
    end
  end

  defp maybe_start_linkedin_task(tasks, ticker, parent_pid, enabled_sources) do
    if Map.get(enabled_sources, :linkedin, true) do
      send(parent_pid, {:data_source_update, :linkedin, :processing})

      Map.put(
        tasks,
        :linkedin,
        Task.async(fn -> LinkedIn.get_linkedin_data(ticker) end)
      )
    else
      tasks
    end
  end

  # Wait for and process results for all enabled tasks
  defp await_and_process_results(tasks, parent_pid) do
    tasks
    |> maybe_await_market_data(parent_pid)
    |> maybe_await_announcement_smart(parent_pid)
    |> maybe_await_announcement_raw(parent_pid)
    |> maybe_await_linkedin(parent_pid)
  end

  defp maybe_await_market_data(tasks, parent_pid) do
    if Map.has_key?(tasks, :market_data) do
      market_result = Task.await(tasks.market_data, 30_000)

      case market_result do
        {:ok, timeseries_data} ->
          send(parent_pid, {:data_source_complete, :market_data, timeseries_data})

        {:error, reason} ->
          send(parent_pid, {:data_source_error, :market_data, reason})
      end
    end

    tasks
  end

  defp maybe_await_announcement_smart(tasks, parent_pid) do
    if Map.has_key?(tasks, :announcement_smart) do
      announcement_smart_result = Task.await(tasks.announcement_smart, 30_000)

      case announcement_smart_result do
        {:ok, announcement_smart_data} ->
          send(parent_pid, {:data_source_complete, :announcement_smart, announcement_smart_data})

        {:error, reason} ->
          send(parent_pid, {:data_source_error, :announcement_smart, reason})
      end
    end

    tasks
  end

  defp maybe_await_announcement_raw(tasks, parent_pid) do
    if Map.has_key?(tasks, :announcement_raw) do
      announcement_raw_result = Task.await(tasks.announcement_raw, 30_000)

      case announcement_raw_result do
        {:ok, announcement_raw_data} ->
          send(parent_pid, {:data_source_complete, :announcement_raw, announcement_raw_data})

        {:error, reason} ->
          send(parent_pid, {:data_source_error, :announcement_raw, reason})
      end
    end

    tasks
  end

  defp maybe_await_linkedin(tasks, parent_pid) do
    if Map.has_key?(tasks, :linkedin) do
      linkedin_result = Task.await(tasks.linkedin, 30_000)

      case linkedin_result do
        {:ok, linkedin_data} ->
          send(parent_pid, {:data_source_complete, :linkedin, linkedin_data})

        {:error, reason} ->
          send(parent_pid, {:data_source_error, :linkedin, reason})
      end
    end

    tasks
  end

  @doc """
  Compile gathered information into final result
  """
  def compile_gathered_info(params) do
    %{
      ticker: ticker,
      my_company_ticker: my_company_ticker,
      my_company_info: my_company_info,
      basic_company_info: basic_company_info,
      market_data: market_data,
      announcement_smart_data: announcement_smart_data,
      announcement_raw_data: announcement_raw_data,
      linkedin_data: linkedin_data,
      agent_type: agent_type
    } = Map.merge(%{agent_type: :bullet_point_description, business_summary: nil}, params)

    debug_mode = Map.get(params, :debug_mode, false)

    # Handle announcements data - for multi-competitor mode, data is already formatted as strings
    announcements_data =
      if is_binary(announcement_smart_data) do
        # Multi-competitor mode - data is already formatted
        announcement_smart_data
      else
        # Single competitor mode - select best from lists
        select_best_announcements(announcement_smart_data, announcement_raw_data)
      end

    # For multi-competitor mode, use the provided business_summary, otherwise extract from basic_company_info
    {company_name, company_description} =
      if Map.get(params, :business_summary) do
        # Multi-competitor mode - use combined business summary
        {"Multiple Competitors", Map.get(params, :business_summary)}
      else
        # Single competitor mode - extract from basic company info
        {extract_company_name(basic_company_info), extract_company_description(basic_company_info)}
      end

    # Extract my company name and description
    my_company_name = extract_company_name(my_company_info)
    my_company_description = extract_company_description(my_company_info)

    gathered_info =
      build_gathered_info(
        ticker,
        market_data,
        company_description,
        company_name,
        announcements_data,
        linkedin_data
      )

    system_prompt =
      case agent_type do
        :bullet_point_description ->
          generate_bullet_point_system_prompt(
            gathered_info,
            my_company_ticker,
            my_company_name,
            my_company_description
          )

        :report_what_matters ->
          generate_analytical_system_prompt(
            gathered_info,
            my_company_ticker,
            my_company_name,
            my_company_description
          )

        :auto_competitor ->
          generate_multi_competitor_system_prompt(
            gathered_info,
            my_company_ticker,
            my_company_name,
            my_company_description,
            debug_mode
          )
      end

    {:ok, %{gathered_info: gathered_info, system_prompt: system_prompt}}
  end

  # Extract company name from basic company info
  defp extract_company_name(nil), do: "Company name not available"
  defp extract_company_name(%{name: name}) when is_binary(name) and name != "", do: name
  defp extract_company_name(_), do: "Company name not available"

  # Extract company description from basic company info
  defp extract_company_description(nil), do: nil

  defp extract_company_description(%{description: description}) when is_binary(description) and description != "",
    do: description

  defp extract_company_description(_), do: nil

  # Select the best available announcement data (prefer smart over raw)
  defp select_best_announcements(announcement_smart_data, announcement_raw_data) do
    cond do
      announcement_smart_data && length(announcement_smart_data) > 0 -> announcement_smart_data
      announcement_raw_data && length(announcement_raw_data) > 0 -> announcement_raw_data
      true -> []
    end
  end

  # Build the gathered info structure
  defp build_gathered_info(ticker, market_data, company_info, company_name, announcements_data, linkedin_data) do
    %{
      ticker: ticker,
      company_name: company_name,
      timeseries_data: market_data,
      financial_data: format_financial_data(market_data),
      business_summary: extract_business_summary(company_info),
      announcements: format_announcements_data(announcements_data),
      linkedin_posts: format_linkedin_data(linkedin_data)
    }
  end

  defp format_financial_data(nil), do: nil
  defp format_financial_data(market_data) when is_binary(market_data), do: market_data
  defp format_financial_data(market_data), do: MarketData.format_timeseries_summary(market_data)

  defp extract_business_summary(nil), do: nil
  defp extract_business_summary(company_info), do: company_info

  defp format_announcements_data(announcements_data)
       when is_list(announcements_data) and length(announcements_data) > 0 do
    Announcements.format_announcements_summary(announcements_data)
  end

  defp format_announcements_data(announcements_data) when is_binary(announcements_data) do
    # Multi-competitor mode - data is already formatted
    announcements_data
  end

  defp format_announcements_data(_), do: nil

  defp format_linkedin_data(linkedin_data) when is_list(linkedin_data) and length(linkedin_data) > 0 do
    LinkedIn.format_linkedin_summary(linkedin_data)
  end

  defp format_linkedin_data(linkedin_data) when is_binary(linkedin_data) do
    # Multi-competitor mode - data is already formatted
    linkedin_data
  end

  defp format_linkedin_data(_), do: nil

  @doc """
  Generate research report using LangChain and OpenAI
  """
  def generate_research_report(system_prompt, ticker) do
    user_message =
      "Please generate a daily competitor intelligence update for #{ticker} based on the provided information. Focus on what executives need to know about this competitor's recent activities."

    case %{llm: ChatOpenAI.new!(%{model: "gpt-4o", temperature: 0.0})}
         |> LLMChain.new!()
         |> LLMChain.add_message(Message.new_system!(system_prompt))
         |> LLMChain.add_message(Message.new_user!(user_message))
         |> LLMChain.run() do
      {:ok, %LLMChain{} = chain} ->
        case List.last(chain.messages) do
          %Message{content: content} when not is_nil(content) ->
            # Sanitize HTML content for security
            sanitized_content = sanitize_html(content)
            {:ok, sanitized_content}

          _ ->
            {:error, "No response received from AI model"}
        end

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        {:error, "Research generation failed: #{inspect(error)}"}
    end
  end

  # Private functions

  defp sanitize_html(content) do
    # Simple HTML sanitization - only allow safe tags and classes
    # In production, consider using HtmlSanitizeEx library
    allowed_tags = ~w(div h3 ul li span strong)

    allowed_classes =
      ~w(space-y-4 space-y-3 text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-start inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0 text-gray-700 dark:text-gray-300)

    content
    |> strip_markdown_code_blocks()
    |> String.replace(~r/<script[^>]*>.*?<\/script>/is, "")
    |> String.replace(~r/<style[^>]*>.*?<\/style>/is, "")
    |> String.replace(~r/on\w+\s*=\s*"[^"]*"/i, "")
    |> String.replace(~r/javascript:/i, "")
    |> sanitize_classes(allowed_classes)
    |> sanitize_tags(allowed_tags)
  end

  defp strip_markdown_code_blocks(content) do
    content
    |> String.replace(~r/```html\s*/i, "")
    |> String.replace(~r/```\s*$/, "")
    |> String.trim()
  end

  defp sanitize_classes(content, allowed_classes) do
    # Remove any class attributes that contain non-allowed classes
    Regex.replace(~r/class\s*=\s*"([^"]*)"/i, content, fn _, classes ->
      filtered_classes =
        classes
        |> String.split()
        |> Enum.filter(&(&1 in allowed_classes))
        |> Enum.join(" ")

      if filtered_classes == "", do: "", else: ~s(class="#{filtered_classes}")
    end)
  end

  defp sanitize_tags(content, allowed_tags) do
    # Remove any HTML tags that are not in the allowed list
    Regex.replace(~r/<\/?([a-zA-Z][a-zA-Z0-9]*)[^>]*>/i, content, fn full_match, tag ->
      if String.downcase(tag) in allowed_tags do
        full_match
      else
        ""
      end
    end)
  end

  defp generate_bullet_point_system_prompt(
         %{
           ticker: ticker,
           company_name: company_name,
           financial_data: financial_data,
           business_summary: business_summary,
           announcements: announcements,
           linkedin_posts: linkedin_posts
         },
         my_company_ticker,
         my_company_name,
         my_company_description
       ) do
    company_display_name =
      if company_name == "Company name not available",
        do: ticker,
        else: "#{company_name} (#{ticker})"

    my_company_display_name =
      if my_company_name == "Company name not available",
        do: my_company_ticker,
        else: "#{my_company_name} (#{my_company_ticker})"

    # Get current date in Australian timezone
    current_date =
      "Australia/Sydney"
      |> DateTime.now!()
      |> DateTime.to_date()
      |> Date.to_string()

    """
    You are a financial analyst creating a daily competitor update for #{my_company_display_name} executives and IR teams. Your role is to provide concise, actionable intelligence about competitor #{company_display_name} that busy executives can quickly digest.

    CURRENT DATE: #{current_date} (Australian time)

    YOUR COMPANY CONTEXT:
    - Company: #{my_company_display_name}
    - Business: #{my_company_description}

    COMPETITOR CONTEXT:
    - Company: #{company_display_name}
    - Business: #{business_summary}

    DAILY UPDATE FOCUS:
    Create a brief, executive-friendly daily update that highlights what's happening with competitor #{company_display_name} TODAY and in recent days. Focus on actionable intelligence that would matter to #{my_company_display_name} executives and IR team monitoring their competitive landscape.

    Please provide a concise daily update with 3-5 bullet points covering:

    **Daily Competitor Intelligence - #{company_display_name}**

    • **Share Price Movement**: [If significant movement today/recently] "#{company_display_name} shares [rose/fell] by X% today following [specific catalyst/announcement]. The stock is now trading at $X, [up/down] Y% over the past week."

    • **Corporate Actions**: [If any announcements] "#{company_display_name} announced [specific action like share buyback, dividend, acquisition, etc.] which [market reaction/significance]."

    • **Executive/Strategic Updates**: [If leadership or strategic announcements] "#{company_display_name} [CEO/management] announced [strategic initiative/leadership change/guidance update] indicating [strategic direction/market positioning]."

    • **Market Sentiment**: [If notable trading patterns or sentiment] "Trading volume was [above/below] average with [institutional/retail] interest. Market sentiment appears [positive/negative/mixed] based on [price action/volume patterns]."

    • **Competitive Intelligence**: [Key takeaway for #{my_company_display_name}] "Key takeaway for #{my_company_display_name}: [What this means for your company - potential threats, opportunities, or strategic implications based on the competitor's activities]."

    STYLE GUIDELINES:
    - Keep each bullet point to 1-2 sentences maximum
    - Focus on facts and immediate implications
    - Use specific numbers and percentages where available
    - Highlight only significant movements (>3% price changes, major announcements, unusual volume)
    - If nothing significant happened, say "No significant developments today"
    - Write in a tone suitable for executive briefings - professional but conversational

    AVAILABLE DATA:
    - Market Data: #{financial_data}
    - Recent Announcements: #{announcements}
    - LinkedIn Activity: #{linkedin_posts}

    Generate the HTML-formatted daily update now:
    """
  end

  defp generate_analytical_system_prompt(
         %{
           ticker: ticker,
           company_name: company_name,
           financial_data: financial_data,
           business_summary: business_summary,
           announcements: announcements,
           linkedin_posts: linkedin_posts
         },
         my_company_ticker,
         my_company_name,
         my_company_description
       ) do
    company_display_name =
      if company_name == "Company name not available",
        do: ticker,
        else: "#{company_name} (#{ticker})"

    my_company_display_name =
      if my_company_name == "Company name not available",
        do: my_company_ticker,
        else: "#{my_company_name} (#{my_company_ticker})"

    # Get current date in Australian timezone
    current_date =
      "Australia/Sydney"
      |> DateTime.now!()
      |> DateTime.to_date()
      |> Date.to_string()

    """
    You are a financial analyst creating a daily competitor update for #{my_company_display_name} executives and IR teams. Your role is to provide concise, actionable intelligence about competitor #{company_display_name} that busy executives can quickly digest.

    CURRENT DATE: #{current_date} (Australian time)

    INTELLIGENCE FILTERING GUIDELINES:
    As an expert analyst, you must determine if the information is significant enough to report to busy executives. Apply these criteria:

    1. SHARE PRICE SIGNIFICANCE: Only report price movements if:
       - Daily change is >2% OR
       - Weekly trend shows >5% movement OR
       - Volume is significantly above average (>150% of typical volume) OR
       - Price movement correlates with specific announcements/events

    2. ANNOUNCEMENT RELEVANCE: Only include announcements that:
       - Materially impact business operations, strategy, or financial position
       - Affect competitive positioning vs #{my_company_display_name}
       - Signal strategic shifts, partnerships, acquisitions, or major contracts
       - Involve leadership changes at C-level or board level
       - Skip routine operational updates unless they reveal strategic insights

    3. LINKEDIN/SOCIAL INTELLIGENCE: Only report if posts:
       - Announce strategic initiatives, partnerships, or major wins
       - Reveal competitive positioning or market expansion plans
       - Show leadership thought leadership that signals strategic direction
       - Skip routine corporate communications or generic industry commentary

    4. COMPETITIVE IMPACT ASSESSMENT: For each piece of information, ask:
       - Does this create a threat or opportunity for #{my_company_display_name}?
       - Does this reveal strategic insights about the competitor's direction?
       - Would #{my_company_display_name} executives want to discuss this in their next strategy meeting?

    If nothing meets these significance thresholds, it's perfectly acceptable to report "No significant developments requiring executive attention today" - this is valuable intelligence too.

    ANALYTICAL THINKING PROCESS:
    Before writing your report, you must analyze the data using this systematic approach:

    1. CROSS-REFERENCE ANALYSIS:
       - If share price moved significantly, examine announcements and LinkedIn posts from the same timeframe
       - Look for correlations: Did an announcement trigger the price movement?
       - Check if LinkedIn activity hints at upcoming developments that could explain market sentiment
       - Identify patterns: Are multiple data sources telling the same story?

    2. SIGNIFICANCE ASSESSMENT:
       - For PRICE MOVEMENTS: Ask "Why did this happen?" and "What does this mean for #{my_company_display_name}?"
       - For ANNOUNCEMENTS: Ask "Does this change the competitive landscape?" and "Should #{my_company_display_name} respond?"
       - For LINKEDIN POSTS: Ask "Does this reveal strategic direction?" and "Is this just marketing or genuine insight?"

    3. COMPETITIVE IMPACT EVALUATION:
       - Does this create a direct threat to #{my_company_display_name}'s market position?
       - Does this reveal an opportunity #{my_company_display_name} should pursue?
       - Would #{my_company_display_name} executives discuss this in their next strategy meeting?
       - Is this something #{my_company_display_name} should monitor or respond to?

    4. EVIDENCE-BASED REASONING:
       - Connect the dots between different data sources
       - Explain WHY something is significant, not just WHAT happened
       - Provide context about how this relates to #{my_company_display_name}'s business
       - If you can't find clear significance, say so explicitly

    YOUR COMPANY CONTEXT:
    #{if my_company_description do
      """
      <MyCompanyInformation>
      Company: #{my_company_display_name}
      Description: #{my_company_description}
      </MyCompanyInformation>
      """
    else
      """
      <MyCompanyInformation>
      Company: #{my_company_display_name}
      </MyCompanyInformation>
      """
    end}

    COMPETITOR INFORMATION:
    Based on the gathered information about #{company_display_name}:

    #{if business_summary do
      """
      <CompetitorGeneralInformation>
      #{business_summary}
      </CompetitorGeneralInformation>
      """
    else
      ""
    end}

    #{if financial_data do
      """
      FINANCIAL DATA (Recent Trading Activity):
      #{financial_data}
      """
    else
      ""
    end}

    #{if announcements do
      """
      <CompanyAnnouncements>
      #{announcements}
      </CompanyAnnouncements>
      """
    else
      ""
    end}

    #{if linkedin_posts do
      """
      <LinkedInPosts>
      #{linkedin_posts}
      </LinkedInPosts>
      """
    else
      ""
    end}

    DAILY UPDATE FOCUS:
    Create a brief, executive-friendly daily update that highlights what's happening with competitor #{company_display_name} TODAY and in recent days. Use your analytical thinking process to determine what's truly significant for #{my_company_display_name} executives.

    REPORTING APPROACH:
    - Only report developments that meet the significance thresholds above
    - Cross-reference data sources to explain WHY things happened
    - Focus on competitive implications for #{my_company_display_name}
    - If nothing significant happened, clearly state this with reasoning

    Please provide a concise daily update with 2-4 bullet points covering ONLY significant developments:

    **Daily Competitor Intelligence - #{company_display_name}**

    • **Share Price Analysis**: [Only if >3% movement OR correlated with announcements] "#{company_display_name} shares [rose/fell] by X% today to $X, driven by [specific catalyst from announcements/LinkedIn]. This [indicates/suggests] [strategic implication for #{my_company_display_name}]."

    • **Strategic Developments**: [Only if material business impact] "#{company_display_name} announced [specific strategic action] which [analysis of competitive impact on #{my_company_display_name}]. This development [creates threat/opportunity/requires monitoring] because [evidence-based reasoning]."

    • **Competitive Intelligence**: [Only if actionable insights] "Key insight for #{my_company_display_name}: [Cross-referenced analysis connecting price movements, announcements, and LinkedIn activity to reveal strategic implications]. Recommended action: [monitor/respond/opportunity to pursue]."

    • **No Significant Activity**: [If nothing meets thresholds] "No significant developments requiring #{my_company_display_name} executive attention today. #{company_display_name} showed [routine trading/standard communications] with no material competitive implications."

    STYLE GUIDELINES:
    - Apply the analytical thinking process before writing anything
    - Cross-reference all data sources to find correlations and explanations
    - Explain WHY developments are significant, not just WHAT happened
    - Connect developments to competitive implications for #{my_company_display_name}
    - Use specific numbers and percentages where available
    - Only include developments that meet the significance thresholds
    - If nothing meets thresholds, explicitly state "No significant developments" with reasoning
    - Write in a tone suitable for executive briefings - analytical but accessible
    - Each bullet point should answer: "Why should #{my_company_display_name} executives care about this?"

    FORMAT REQUIREMENTS:
    IMPORTANT: Return ONLY the HTML content without any markdown formatting, code blocks, or ```html tags.

    Use clean HTML with Tailwind CSS classes for proper formatting. Structure your response as:

    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Daily Intelligence - #{company_display_name}</h3>
      <ul class="space-y-3">
        <li class="flex items-start">
          <span class="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
          <div>
            <strong class="text-gray-900 dark:text-white">Share Price Movement:</strong>
            <span class="text-gray-700 dark:text-gray-300">Your content here...</span>
          </div>
        </li>
        <!-- Repeat for other categories -->
      </ul>
    </div>

    Only use these HTML tags: div, h3, ul, li, span, strong. Only use these Tailwind classes: space-y-4, space-y-3, text-lg, font-semibold, text-gray-900, dark:text-white, mb-3, flex, items-start, inline-block, w-2, h-2, bg-blue-500, rounded-full, mt-2, mr-3, flex-shrink-0, text-gray-700, dark:text-gray-300.

    Do NOT wrap your response in ```html or any other markdown formatting.
    """
  end

  # Generate multi-competitor system prompt for auto competitor mode
  defp generate_multi_competitor_system_prompt(
         gathered_info,
         my_company_ticker,
         my_company_name,
         my_company_description,
         debug_mode
       ) do
    my_company_display_name = format_company_display_name(my_company_name, my_company_ticker)
    current_date = get_current_date_string()

    prompt_sections = %{
      debug_requirements: build_debug_requirements_section(debug_mode),
      factual_guidelines: build_factual_guidelines_section(debug_mode),
      analytical_thinking: build_analytical_thinking_section(my_company_display_name),
      company_context: build_company_context_section(my_company_display_name, my_company_description),
      competitor_info: build_competitor_info_section(gathered_info)
    }

    # For multi-competitor mode, we'll structure this differently
    # This is a placeholder that will be enhanced in the next task
    """
    You are a financial analyst creating a factual competitor intelligence update for #{my_company_display_name} executives and IR teams. Your role is to provide objective, data-driven intelligence about multiple competitors based strictly on the provided information.

    CURRENT DATE: #{current_date} (Australian time)

    #{prompt_sections.debug_requirements}

    MULTI-COMPETITOR ANALYSIS STRUCTURE:
    Your report should have two main sections:

    1. EXECUTIVE HIGHLIGHTS (The single most important factual development across ALL competitors)
    2. DETAILED COMPETITOR ANALYSIS (Individual sections for each competitor)

    #{prompt_sections.factual_guidelines}

    #{prompt_sections.analytical_thinking}

    #{prompt_sections.company_context}

    #{prompt_sections.competitor_info}

    FORMAT REQUIREMENTS:
    IMPORTANT: Return ONLY the HTML content without any markdown formatting, code blocks, or ```html tags.

    Use clean HTML with Tailwind CSS classes optimized for email clients. Structure your response as:

    <div class="space-y-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">🎯 Executive Highlight</h3>
        <ul class="space-y-3">
          <li class="flex items-start">
            <span class="inline-block w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            <div>
              <strong class="text-gray-900 dark:text-white">Most Critical Development:</strong>
              <span class="text-gray-700 dark:text-gray-300">State the most significant factual development from the provided data...</span>
            </div>
          </li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">📊 Detailed Competitor Analysis</h3>
        <ul class="space-y-3">
          <li class="flex items-start">
            <span class="inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            <div>
              <strong class="text-gray-900 dark:text-white">Competitor Name:</strong>
              <span class="text-gray-700 dark:text-gray-300">Factual summary of this competitor's documented activities...</span>
            </div>
          </li>
          <!-- Repeat for each competitor -->
        </ul>
      </div>
    </div>

    EMAIL OPTIMIZATION NOTES:
    - Use simple HTML structure for maximum email client compatibility
    - Tailwind classes will be processed by the email template's CSS
    - Keep content concise and scannable for busy executives
    - Use bullet points with colored indicators for visual hierarchy
    - Ensure each competitor gets its own bullet point in the detailed analysis

    Only use these HTML tags: div, h3, ul, li, span, strong. Only use these Tailwind classes: space-y-6, space-y-3, text-lg, font-semibold, text-gray-900, dark:text-white, mb-3, flex, items-start, inline-block, w-2, h-2, bg-red-500, bg-blue-500, rounded-full, mt-2, mr-3, flex-shrink-0, text-gray-700, dark:text-gray-300.

    CRITICAL: Base your report ONLY on the factual data provided. Do not speculate, extrapolate, or add interpretations beyond what is explicitly stated in the source material.

    Do NOT wrap your response in ```html or any other markdown formatting.
    """
  end

  # Helper functions for system prompt generation

  defp format_company_display_name(company_name, company_ticker) do
    if company_name != "" && company_name != "Company name not available",
      do: "#{company_name} (#{company_ticker})",
      else: company_ticker
  end

  defp get_current_date_string do
    "Australia/Sydney"
    |> DateTime.now!()
    |> DateTime.to_date()
    |> Date.to_string()
  end

  defp build_debug_requirements_section(debug_mode) do
    if debug_mode do
      """
      DEBUG MODE REQUIREMENTS:
      This is DEBUG MODE - you MUST provide source attribution for EVERY statement you make. For each piece of information, clearly indicate where it came from using this format:

      - For market data: "(From Market Data: [specific detail])"
      - For announcements: "(From Announcement: [announcement title or key detail])"
      - For LinkedIn posts: "(From LinkedIn: [post summary])"
      - For company information: "(From Company Profile: [specific detail])"

      EXAMPLE FORMAT:
      "Solvar Ltd has been actively engaging in an on-market share buy-back program (From Announcement: On-Market Share Buy-Back Notice), having repurchased a total of 9,537,568 shares, with a total consideration of AUD 11,549,826.27 (From Market Data: Trading Summary)."

      """
    else
      ""
    end
  end

  defp build_analytical_thinking_section(my_company_display_name) do
    """
    ANALYTICAL THINKING PROCESS:
    Before writing your report, you must analyze the data using this systematic approach:

    1. CROSS-REFERENCE ANALYSIS:
       - If share price moved significantly, examine announcements and LinkedIn posts from the same timeframe
       - Look for correlations: Did an announcement trigger the price movement?
       - Check if LinkedIn activity hints at upcoming developments that could explain market sentiment
       - Identify patterns: Are multiple data sources telling the same story?

    2. SIGNIFICANCE ASSESSMENT:
       - For PRICE MOVEMENTS: Ask "Why did this happen?" and "What does this mean for #{my_company_display_name}?"
       - For ANNOUNCEMENTS: Ask "Does this change the competitive landscape?" and "Should #{my_company_display_name} respond?"
       - For LINKEDIN POSTS: Ask "Does this reveal strategic direction?" and "Is this just marketing or genuine insight?"

    3. COMPETITIVE IMPACT EVALUATION:
       - Does this create a direct threat to #{my_company_display_name}'s market position?
       - Does this reveal an opportunity #{my_company_display_name} should pursue?
       - Would #{my_company_display_name} executives discuss this in their next strategy meeting?
       - Is this something #{my_company_display_name} should monitor or respond to?

    4. EVIDENCE-BASED REASONING:
       - Connect the dots between different data sources
       - Explain WHY something is significant, not just WHAT happened
       - Provide context about how this relates to #{my_company_display_name}'s business
       - If you can't find clear significance, say so explicitly
    """
  end

  defp build_factual_guidelines_section(debug_mode) do
    base_guidelines = """
    INTELLIGENCE FILTERING GUIDELINES:
    As an expert analyst, you must determine if the information is significant enough to report to busy executives. Apply these criteria:

    1. SHARE PRICE SIGNIFICANCE: Only report price movements if:
       - Daily change is >2% OR
       - Weekly trend shows >5% movement OR
       - Volume is significantly above average (>150% of typical volume) OR
       - Price movement correlates with specific announcements/events

    2. ANNOUNCEMENT RELEVANCE: Only include announcements that:
       - Materially impact business operations, strategy, or financial position
       - Affect competitive positioning vs your company
       - Signal strategic shifts, partnerships, acquisitions, or major contracts
       - Involve leadership changes at C-level or board level
       - Skip routine operational updates unless they reveal strategic insights

    3. LINKEDIN/SOCIAL INTELLIGENCE: Only report if posts:
       - Announce strategic initiatives, partnerships, or major wins
       - Reveal competitive positioning or market expansion plans
       - Show leadership thought leadership that signals strategic direction
       - Skip routine corporate communications or generic industry commentary

    4. COMPETITIVE IMPACT ASSESSMENT: For each piece of information, ask:
       - Does this create a threat or opportunity for your company?
       - Does this reveal strategic insights about the competitor's direction?
       - Would your company executives want to discuss this in their next strategy meeting?

    If nothing meets these significance thresholds, it's perfectly acceptable to report "No significant developments requiring executive attention today" - this is valuable intelligence too.

    FACTUAL REPORTING GUIDELINES:
    - Report ONLY on factual developments found in the provided data
    - Do NOT speculate, infer, or imagine potential impacts
    - Focus on concrete events: price movements, announcements, LinkedIn posts
    - Avoid subjective language and stick to objective observations
    - If no significant developments occurred, state this clearly
    """

    if debug_mode do
      base_guidelines <>
        "    - ALWAYS include source attribution in parentheses after each statement\n"
    else
      base_guidelines
    end
  end

  defp build_company_context_section(company_display_name, company_description) do
    if company_description do
      """
      YOUR COMPANY CONTEXT:
      <MyCompanyInformation>
      Company: #{company_display_name}
      Description: #{company_description}
      </MyCompanyInformation>
      """
    else
      """
      YOUR COMPANY CONTEXT:
      <MyCompanyInformation>
      Company: #{company_display_name}
      </MyCompanyInformation>
      """
    end
  end

  defp build_competitor_info_section(gathered_info) do
    business_summary = Map.get(gathered_info, :business_summary)
    financial_data = Map.get(gathered_info, :financial_data)
    announcements = Map.get(gathered_info, :announcements)
    linkedin_posts = Map.get(gathered_info, :linkedin_posts)

    sections = [
      build_business_summary_section(business_summary),
      build_financial_data_section(financial_data),
      build_announcements_section(announcements),
      build_linkedin_section(linkedin_posts)
    ]

    "COMPETITOR INFORMATION:\n" <> Enum.join(sections, "\n")
  end

  defp build_business_summary_section(business_summary) do
    if business_summary do
      """
      <CompetitorGeneralInformation>
      #{business_summary}
      </CompetitorGeneralInformation>
      """
    else
      ""
    end
  end

  defp build_financial_data_section(financial_data) do
    if financial_data do
      """
      <TradingActivity>
      #{financial_data}
      </TradingActivity>
      """
    else
      ""
    end
  end

  defp build_announcements_section(announcements) do
    if announcements do
      """
      <CompanyAnnouncements>
      #{announcements}
      </CompanyAnnouncements>
      """
    else
      ""
    end
  end

  defp build_linkedin_section(linkedin_posts) do
    if linkedin_posts do
      """
      <LinkedInActivity>
      #{linkedin_posts}
      </LinkedInActivity>
      """
    else
      ""
    end
  end
end
