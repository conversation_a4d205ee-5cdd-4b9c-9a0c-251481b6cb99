defmodule PublicApiWeb.MarketDataController do
  @moduledoc """
  Controller for market data endpoints
  """
  use PublicApiWeb, :controller

  import Ecto.Query

  @error_message "Unfortunately we could not get market data at this time, please try again later or contact us for support."

  @doc """
  Gets timeseries data for a given ticker
  Expects query parameters: ticker, start_date, end_date
  """
  def timeseries(
        conn,
        %{"ticker" => ticker, "start_date" => start_date_str, "end_date" => end_date_str} = params
      ) do
    with {:ok, start_date} <- parse_date(start_date_str),
         {:ok, end_date} <- parse_date(end_date_str),
         {:ok, timeseries_data} <-
           Refinitiv.Timeseries.get_interday_v5(ticker, start_date, end_date, parse_opts(params)) do
      json(conn, %{
        ticker: ticker,
        start_date: start_date,
        end_date: end_date,
        data: timeseries_data
      })
    else
      {:error, :invalid_date} ->
        conn
        |> put_status(400)
        |> json(%{
          error: 400,
          message: "Invalid date format. Please use YYYY-MM-DD format."
        })

      {:error, _reason} ->
        conn
        |> put_status(500)
        |> json(%{
          error: 500,
          message: @error_message
        })
    end
  end

  def timeseries(conn, _params) do
    conn
    |> put_status(400)
    |> json(%{
      error: 400,
      message: "Missing required parameters: ticker, start_date, end_date"
    })
  end

  @doc """
  Gets company general information for a given ticker
  """
  def company_information(conn, %{"ticker" => ticker}) do
    case Gaia.Markets.get_or_insert_company_information_description("asx", ticker) do
      {:ok, description} ->
        json(conn, %{
          ticker: ticker,
          company_info: description
        })

      {:error, _reason} ->
        conn
        |> put_status(500)
        |> json(%{
          error: 500,
          message: @error_message
        })
    end
  end

  def company_information(conn, _params) do
    conn
    |> put_status(400)
    |> json(%{
      error: 400,
      message: "Missing required parameter: ticker"
    })
  end

  @doc """
  Searches announcements with filters and ordering options
  Accepts POST body with filters and orders similar to announcements_list_query
  """
  def search_announcements(conn, params) do
    with {:ok, search_params} <- parse_announcements_search_params(params),
         {:ok, announcements} <- execute_announcements_search(search_params) do
      json(conn, %{
        announcements: announcements,
        filters: search_params[:filters] || [],
        orders: search_params[:orders] || []
      })
    else
      {:error, :invalid_params} ->
        conn
        |> put_status(400)
        |> json(%{
          error: 400,
          message: "Invalid search parameters. Expected filters and/or orders arrays."
        })

      {:error, _reason} ->
        conn
        |> put_status(500)
        |> json(%{
          error: 500,
          message: @error_message
        })
    end
  end

  # Parse date string and return proper error for invalid dates
  defp parse_date(date_str) do
    case Date.from_iso8601(date_str) do
      {:ok, date} -> {:ok, date}
      {:error, _} -> {:error, :invalid_date}
    end
  end

  # Parse optional parameters for timeseries
  defp parse_opts(params) do
    opts = []

    opts =
      if Map.has_key?(params, "use_non_adjusted") do
        case params["use_non_adjusted"] do
          "true" -> Keyword.put(opts, :use_non_adjusted, true)
          "false" -> Keyword.put(opts, :use_non_adjusted, false)
          _ -> opts
        end
      else
        opts
      end

    opts
  end

  # Parse and validate announcements search parameters
  defp parse_announcements_search_params(params) do
    try do
      search_params = []

      # Parse filters if provided
      search_params =
        if Map.has_key?(params, "filters") and is_list(params["filters"]) do
          filters = Enum.map(params["filters"], &parse_filter/1)
          Keyword.put(search_params, :filters, filters)
        else
          search_params
        end

      # Parse orders if provided
      search_params =
        if Map.has_key?(params, "orders") and is_list(params["orders"]) do
          orders = Enum.map(params["orders"], &parse_order/1)
          Keyword.put(search_params, :orders, orders)
        else
          search_params
        end

      {:ok, search_params}
    rescue
      _ -> {:error, :invalid_params}
    end
  end

  # Parse individual filter
  defp parse_filter(%{"key" => key, "value" => value}) when is_binary(key) do
    %{key: key, value: value}
  end

  defp parse_filter(_), do: raise("Invalid filter format")

  # Parse individual order
  defp parse_order(%{"key" => key, "value" => value}) when is_binary(key) and is_binary(value) do
    %{key: key, value: value}
  end

  defp parse_order(_), do: raise("Invalid order format")

  # Execute the announcements search using Gaia.Interactions
  defp execute_announcements_search(search_params) do
    try do
      announcements =
        search_params
        |> Gaia.Interactions.announcements_list_query()
        |> select([ma, _m, pa], %{
          media_announcement: ma,
          prepared_announcement: pa
        })
        |> Gaia.Repo.all()
        |> Enum.map(&format_announcement_result/1)

      {:ok, announcements}
    rescue
      error ->
        {:error, error}
    end
  end

  # Format the announcement result for API response
  defp format_announcement_result(%{media_announcement: nil, prepared_announcement: pa}) do
    %{
      type: "prepared_announcement",
      id: pa.id,
      title: pa.title,
      summary: pa.summary,
      is_draft: pa.is_draft,
      inserted_at: pa.inserted_at,
      updated_at: pa.updated_at,
      video_url: pa.video_url,
      social_video_url: pa.social_video_url
    }
  end

  defp format_announcement_result(%{media_announcement: %{id: nil}, prepared_announcement: pa}) do
    format_announcement_result(%{media_announcement: nil, prepared_announcement: pa})
  end

  defp format_announcement_result(%{media_announcement: ma, prepared_announcement: _pa}) do
    %{
      type: "media_announcement",
      id: ma.id,
      header: ma.header,
      listing_key: ma.listing_key,
      market_key: ma.market_key,
      market_sensitive: ma.market_sensitive,
      posted_at: ma.posted_at,
      summary: ma.summary,
      summary_ai: ma.summary_ai,
      url: ma.url,
      video_url: ma.video_url,
      social_video_url: ma.social_video_url,
      featured_on_hub: ma.featured_on_hub
    }
  end
end
