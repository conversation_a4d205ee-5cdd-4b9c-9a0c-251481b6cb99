defmodule PublicApiWeb.Router do
  use PublicApiWeb, :router

  import PublicApiWeb.CurrentCompanyProfilePlug

  pipeline :api do
    plug :accepts, ["json"]
    plug Helper.PublicIpPlug
    plug PublicApiWeb.BanCloudIpPlug
  end

  pipeline :current_company do
    plug :fetch_current_company_profile
  end

  pipeline :api_key_auth do
    plug PublicApiWeb.Plugs.Auth
  end

  scope "/api", PublicApiWeb do
    pipe_through [:api, :current_company]

    post "/subscribe", ContactSubscriptionsController, :subscribe
  end

  scope "/api/listed", PublicApiWeb do
    pipe_through [:api, :api_key_auth]

    get "/market-data", LicatController, :market_data
    post "/add-tickers", LicatController, :add_tickers
  end

  scope "/api/market_data", PublicApiWeb do
    pipe_through [:api, :api_key_auth]

    get "/timeseries", MarketDataController, :timeseries
    get "/company_information", MarketDataController, :company_information
  end

  scope "/api/announcements", PublicApiWeb do
    pipe_through [:api, :api_key_auth]

    post "/search_announcements", MarketDataController, :search_announcements
  end
end
